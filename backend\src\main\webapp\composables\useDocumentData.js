import { ref, reactive, computed } from 'vue'

export function useDocumentData(classId, documentId) {
  // Document data
  const document = ref({
    id: documentId,
    title: 'Class 5 May 2025',
    classId: classId,
    lastEdited: new Date().toLocaleDateString('en-GB'),
    editor: 'Mr. <PERSON>'
  })

  // Questions data
  const overallQuestions = ref([
    {
      id: 1,
      question: 'Is every student nice to each other?',
      answer: 'yes',
      note: 'Can you elaborate on this'
    },
    {
      id: 2,
      question: 'How are you controlling the class when they are too loud?',
      answer: 'fix that',
      note: ''
    },
    {
      id: 3,
      question: 'Did anything notable happen recently?',
      answer: '<PERSON> is still eating his boogers',
      note: ''
    }
  ])

  const subjectQuestions = ref([
    {
      id: 1,
      subject: 'Math',
      question: 'Has the school norm been achieved?',
      answer: 'yes',
      note: ''
    },
    {
      id: 2,
      subject: 'Math',
      question: 'Who is good at math?',
      answer: 'not <PERSON>',
      note: ''
    },
    {
      id: 3,
      subject: 'Math',
      question: 'Ho do you teach math?',
      answer: 'book',
      note: ''
    }
  ])

  const studentQuestions = ref([
    {
      id: 1,
      student: '<PERSON>',
      question: 'How is <PERSON>\'s reading',
      answer: 'yes',
      note: ''
    },
    {
      id: 2,
      student: '<PERSON>',
      question: 'Is <PERSON> getting bullied',
      answer: 'he should be',
      note: ''
    },
    {
      id: 3,
      student: 'Jimmy',
      question: 'Anything else?',
      answer: 'nobody likes him',
      note: ''
    }
  ])

  // Available subjects and students
  const subjects = ref(['Reading', 'Dutch', 'English', 'Math', 'PE'])
  const students = ref(['brock', 'Ash', 'Pikachu', 'Jimmy', 'girl name'])

  // Selected filters
  const selectedSubject = ref('Math')
  const selectedStudent = ref('Jimmy')

  // General notes
  const generalNotes = ref([
    { id: 1, text: 'Please be more specific', author: 'Mr. Smith', timestamp: new Date() }
  ])

  // Actions/Checklist
  const actions = ref([
    { id: 1, text: 'Pay more attention to Jimmy', completed: true, createdBy: 'teaching_coach' }
  ])

  // Floating notes
  const floatingNotes = ref([])

  // Computed filtered questions
  const filteredSubjectQuestions = computed(() => 
    subjectQuestions.value.filter(q => q.subject === selectedSubject.value)
  )

  const filteredStudentQuestions = computed(() => 
    studentQuestions.value.filter(q => q.student === selectedStudent.value)
  )

  // CRUD operations
  const addOverallQuestion = (questionText) => {
    const newQuestion = {
      id: Date.now(),
      question: questionText,
      answer: '',
      note: ''
    }
    overallQuestions.value.push(newQuestion)
    return newQuestion
  }

  const addSubjectQuestion = (questionText) => {
    const newQuestion = {
      id: Date.now(),
      subject: selectedSubject.value,
      question: questionText,
      answer: '',
      note: ''
    }
    subjectQuestions.value.push(newQuestion)
    return newQuestion
  }

  const addStudentQuestion = (questionText) => {
    const newQuestion = {
      id: Date.now(),
      student: selectedStudent.value,
      question: questionText,
      answer: '',
      note: ''
    }
    studentQuestions.value.push(newQuestion)
    return newQuestion
  }

  const updateAnswer = (questionType, questionId, answer) => {
    let questions
    switch (questionType) {
      case 'overall':
        questions = overallQuestions.value
        break
      case 'subject':
        questions = subjectQuestions.value
        break
      case 'student':
        questions = studentQuestions.value
        break
    }
    const question = questions.find(q => q.id === questionId)
    if (question) {
      question.answer = answer
    }
  }

  const addGeneralNote = (noteText) => {
    const newNote = {
      id: Date.now(),
      text: noteText,
      author: 'Current User',
      timestamp: new Date()
    }
    generalNotes.value.push(newNote)
    return newNote
  }

  const addAction = (actionText) => {
    const newAction = {
      id: Date.now(),
      text: actionText,
      completed: false,
      createdBy: 'teaching_coach'
    }
    actions.value.push(newAction)
    return newAction
  }

  const toggleAction = (actionId) => {
    const action = actions.value.find(a => a.id === actionId)
    if (action) {
      action.completed = !action.completed
    }
  }

  const addFloatingNote = (targetType, targetId, noteText, position) => {
    const newNote = {
      id: Date.now(),
      targetType, // 'question', 'answer'
      targetId,
      text: noteText,
      position,
      author: 'Current User',
      timestamp: new Date()
    }
    floatingNotes.value.push(newNote)
    return newNote
  }

  const saveDocument = async () => {
    // Here you would make API calls to save all the data
    try {
      // Mock save operation
      console.log('Saving document...', {
        document: document.value,
        overallQuestions: overallQuestions.value,
        subjectQuestions: subjectQuestions.value,
        studentQuestions: studentQuestions.value,
        generalNotes: generalNotes.value,
        actions: actions.value,
        floatingNotes: floatingNotes.value
      })
      
      // Update last edited timestamp
      document.value.lastEdited = new Date().toLocaleDateString('en-GB')
      
      return { success: true, message: 'Document saved successfully!' }
    } catch (error) {
      return { success: false, message: 'Failed to save document: ' + error.message }
    }
  }

  return {
    // Data
    document,
    overallQuestions,
    subjectQuestions,
    studentQuestions,
    subjects,
    students,
    selectedSubject,
    selectedStudent,
    generalNotes,
    actions,
    floatingNotes,
    
    // Computed
    filteredSubjectQuestions,
    filteredStudentQuestions,
    
    // Methods
    addOverallQuestion,
    addSubjectQuestion,
    addStudentQuestion,
    updateAnswer,
    addGeneralNote,
    addAction,
    toggleAction,
    addFloatingNote,
    saveDocument
  }
}
