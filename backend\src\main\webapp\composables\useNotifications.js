import { ref, computed } from 'vue'

export function useNotifications() {
  // Mock notifications data
  const notifications = ref([
    {
      id: 1,
      message: 'Mr <PERSON> updated a document (Click to view)',
      classId: 1,
      documentId: 1,
      timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      read: false
    },
    {
      id: 2,
      message: 'Mr <PERSON> updated a document (Click to view)',
      classId: 2,
      documentId: 2,
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      read: false
    }
  ])

  // Computed properties
  const unreadCount = computed(() => 
    notifications.value.filter(n => !n.read).length
  )

  const sortedNotifications = computed(() => 
    [...notifications.value].sort((a, b) => b.timestamp - a.timestamp)
  )

  // Methods
  const markAsRead = (notificationId) => {
    const notification = notifications.value.find(n => n.id === notificationId)
    if (notification) {
      notification.read = true
    }
  }

  const markAllAsRead = () => {
    notifications.value.forEach(n => n.read = true)
  }

  const addNotification = (message, classId, documentId) => {
    const newNotification = {
      id: Date.now(),
      message,
      classId,
      documentId,
      timestamp: new Date(),
      read: false
    }
    notifications.value.unshift(newNotification)
    return newNotification
  }

  const removeNotification = (notificationId) => {
    const index = notifications.value.findIndex(n => n.id === notificationId)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  return {
    notifications,
    unreadCount,
    sortedNotifications,
    markAsRead,
    markAllAsRead,
    addNotification,
    removeNotification
  }
}
