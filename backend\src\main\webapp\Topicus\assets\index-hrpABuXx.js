(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const r of i.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&s(r)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function s(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();/**
* @vue/shared v3.5.15
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function $s(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const ae={},Ht=[],et=()=>{},fr=()=>!1,Dn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Es=e=>e.startsWith("onUpdate:"),ke=Object.assign,Rs=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},dr=Object.prototype.hasOwnProperty,ne=(e,t)=>dr.call(e,t),H=Array.isArray,Vt=e=>Fn(e)==="[object Map]",Vo=e=>Fn(e)==="[object Set]",q=e=>typeof e=="function",be=e=>typeof e=="string",gt=e=>typeof e=="symbol",he=e=>e!==null&&typeof e=="object",Uo=e=>(he(e)||q(e))&&q(e.then)&&q(e.catch),Ko=Object.prototype.toString,Fn=e=>Ko.call(e),hr=e=>Fn(e).slice(8,-1),qo=e=>Fn(e)==="[object Object]",ks=e=>be(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,nn=$s(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),jn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},pr=/-(\w)/g,Ke=jn(e=>e.replace(pr,(t,n)=>n?n.toUpperCase():"")),mr=/\B([A-Z])/g,Et=jn(e=>e.replace(mr,"-$1").toLowerCase()),Ln=jn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Yn=jn(e=>e?`on${Ln(e)}`:""),At=(e,t)=>!Object.is(e,t),$n=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Qo=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},ds=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Ws;const Bn=()=>Ws||(Ws=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Hn(e){if(H(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],o=be(s)?yr(s):Hn(s);if(o)for(const i in o)t[i]=o[i]}return t}else if(be(e)||he(e))return e}const gr=/;(?![^(]*\))/g,vr=/:([^]+)/,_r=/\/\*[^]*?\*\//g;function yr(e){const t={};return e.replace(_r,"").split(gr).forEach(n=>{if(n){const s=n.split(vr);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Me(e){let t="";if(be(e))t=e;else if(H(e))for(let n=0;n<e.length;n++){const s=Me(e[n]);s&&(t+=s+" ")}else if(he(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const br="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",wr=$s(br);function Wo(e){return!!e||e===""}const zo=e=>!!(e&&e.__v_isRef===!0),Q=e=>be(e)?e:e==null?"":H(e)||he(e)&&(e.toString===Ko||!q(e.toString))?zo(e)?Q(e.value):JSON.stringify(e,Go,2):String(e),Go=(e,t)=>zo(t)?Go(e,t.value):Vt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,o],i)=>(n[Xn(s,i)+" =>"]=o,n),{})}:Vo(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Xn(n))}:gt(t)?Xn(t):he(t)&&!H(t)&&!qo(t)?String(t):t,Xn=(e,t="")=>{var n;return gt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.15
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ie;class xr{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ie,!t&&Ie&&(this.index=(Ie.scopes||(Ie.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Ie;try{return Ie=this,t()}finally{Ie=n}}}on(){++this._on===1&&(this.prevScope=Ie,Ie=this)}off(){this._on>0&&--this._on===0&&(Ie=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function Cr(){return Ie}let fe;const Zn=new WeakSet;class Jo{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ie&&Ie.active&&Ie.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Zn.has(this)&&(Zn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Xo(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,zs(this),Zo(this);const t=fe,n=qe;fe=this,qe=!0;try{return this.fn()}finally{ei(this),fe=t,qe=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ns(t);this.deps=this.depsTail=void 0,zs(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Zn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){hs(this)&&this.run()}get dirty(){return hs(this)}}let Yo=0,sn,on;function Xo(e,t=!1){if(e.flags|=8,t){e.next=on,on=e;return}e.next=sn,sn=e}function Ps(){Yo++}function Ts(){if(--Yo>0)return;if(on){let t=on;for(on=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;sn;){let t=sn;for(sn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Zo(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function ei(e){let t,n=e.depsTail,s=n;for(;s;){const o=s.prevDep;s.version===-1?(s===n&&(n=o),Ns(s),Sr(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=o}e.deps=t,e.depsTail=n}function hs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(ti(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function ti(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===un)||(e.globalVersion=un,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!hs(e))))return;e.flags|=2;const t=e.dep,n=fe,s=qe;fe=e,qe=!0;try{Zo(e);const o=e.fn(e._value);(t.version===0||At(o,e._value))&&(e.flags|=128,e._value=o,t.version++)}catch(o){throw t.version++,o}finally{fe=n,qe=s,ei(e),e.flags&=-3}}function Ns(e,t=!1){const{dep:n,prevSub:s,nextSub:o}=e;if(s&&(s.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)Ns(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Sr(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let qe=!0;const ni=[];function dt(){ni.push(qe),qe=!1}function ht(){const e=ni.pop();qe=e===void 0?!0:e}function zs(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=fe;fe=void 0;try{t()}finally{fe=n}}}let un=0;class Ar{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Os{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!fe||!qe||fe===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==fe)n=this.activeLink=new Ar(fe,this),fe.deps?(n.prevDep=fe.depsTail,fe.depsTail.nextDep=n,fe.depsTail=n):fe.deps=fe.depsTail=n,si(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=fe.depsTail,n.nextDep=void 0,fe.depsTail.nextDep=n,fe.depsTail=n,fe.deps===n&&(fe.deps=s)}return n}trigger(t){this.version++,un++,this.notify(t)}notify(t){Ps();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Ts()}}}function si(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)si(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const ps=new WeakMap,Nt=Symbol(""),ms=Symbol(""),fn=Symbol("");function $e(e,t,n){if(qe&&fe){let s=ps.get(e);s||ps.set(e,s=new Map);let o=s.get(n);o||(s.set(n,o=new Os),o.map=s,o.key=n),o.track()}}function ut(e,t,n,s,o,i){const r=ps.get(e);if(!r){un++;return}const l=c=>{c&&c.trigger()};if(Ps(),t==="clear")r.forEach(l);else{const c=H(e),f=c&&ks(n);if(c&&n==="length"){const a=Number(s);r.forEach((u,p)=>{(p==="length"||p===fn||!gt(p)&&p>=a)&&l(u)})}else switch((n!==void 0||r.has(void 0))&&l(r.get(n)),f&&l(r.get(fn)),t){case"add":c?f&&l(r.get("length")):(l(r.get(Nt)),Vt(e)&&l(r.get(ms)));break;case"delete":c||(l(r.get(Nt)),Vt(e)&&l(r.get(ms)));break;case"set":Vt(e)&&l(r.get(Nt));break}}Ts()}function Dt(e){const t=te(e);return t===e?t:($e(t,"iterate",fn),Ve(e)?t:t.map(Se))}function Vn(e){return $e(e=te(e),"iterate",fn),e}const $r={__proto__:null,[Symbol.iterator](){return es(this,Symbol.iterator,Se)},concat(...e){return Dt(this).concat(...e.map(t=>H(t)?Dt(t):t))},entries(){return es(this,"entries",e=>(e[1]=Se(e[1]),e))},every(e,t){return lt(this,"every",e,t,void 0,arguments)},filter(e,t){return lt(this,"filter",e,t,n=>n.map(Se),arguments)},find(e,t){return lt(this,"find",e,t,Se,arguments)},findIndex(e,t){return lt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return lt(this,"findLast",e,t,Se,arguments)},findLastIndex(e,t){return lt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return lt(this,"forEach",e,t,void 0,arguments)},includes(...e){return ts(this,"includes",e)},indexOf(...e){return ts(this,"indexOf",e)},join(e){return Dt(this).join(e)},lastIndexOf(...e){return ts(this,"lastIndexOf",e)},map(e,t){return lt(this,"map",e,t,void 0,arguments)},pop(){return Xt(this,"pop")},push(...e){return Xt(this,"push",e)},reduce(e,...t){return Gs(this,"reduce",e,t)},reduceRight(e,...t){return Gs(this,"reduceRight",e,t)},shift(){return Xt(this,"shift")},some(e,t){return lt(this,"some",e,t,void 0,arguments)},splice(...e){return Xt(this,"splice",e)},toReversed(){return Dt(this).toReversed()},toSorted(e){return Dt(this).toSorted(e)},toSpliced(...e){return Dt(this).toSpliced(...e)},unshift(...e){return Xt(this,"unshift",e)},values(){return es(this,"values",Se)}};function es(e,t,n){const s=Vn(e),o=s[t]();return s!==e&&!Ve(e)&&(o._next=o.next,o.next=()=>{const i=o._next();return i.value&&(i.value=n(i.value)),i}),o}const Er=Array.prototype;function lt(e,t,n,s,o,i){const r=Vn(e),l=r!==e&&!Ve(e),c=r[t];if(c!==Er[t]){const u=c.apply(e,i);return l?Se(u):u}let f=n;r!==e&&(l?f=function(u,p){return n.call(this,Se(u),p,e)}:n.length>2&&(f=function(u,p){return n.call(this,u,p,e)}));const a=c.call(r,f,s);return l&&o?o(a):a}function Gs(e,t,n,s){const o=Vn(e);let i=n;return o!==e&&(Ve(e)?n.length>3&&(i=function(r,l,c){return n.call(this,r,l,c,e)}):i=function(r,l,c){return n.call(this,r,Se(l),c,e)}),o[t](i,...s)}function ts(e,t,n){const s=te(e);$e(s,"iterate",fn);const o=s[t](...n);return(o===-1||o===!1)&&Ds(n[0])?(n[0]=te(n[0]),s[t](...n)):o}function Xt(e,t,n=[]){dt(),Ps();const s=te(e)[t].apply(e,n);return Ts(),ht(),s}const Rr=$s("__proto__,__v_isRef,__isVue"),oi=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(gt));function kr(e){gt(e)||(e=String(e));const t=te(this);return $e(t,"has",e),t.hasOwnProperty(e)}class ii{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const o=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!o;if(n==="__v_isReadonly")return o;if(n==="__v_isShallow")return i;if(n==="__v_raw")return s===(o?i?Lr:ai:i?ci:li).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const r=H(t);if(!o){let c;if(r&&(c=$r[n]))return c;if(n==="hasOwnProperty")return kr}const l=Reflect.get(t,n,Re(t)?t:s);return(gt(n)?oi.has(n):Rr(n))||(o||$e(t,"get",n),i)?l:Re(l)?r&&ks(n)?l:l.value:he(l)?o?fi(l):Ot(l):l}}class ri extends ii{constructor(t=!1){super(!1,t)}set(t,n,s,o){let i=t[n];if(!this._isShallow){const c=$t(i);if(!Ve(s)&&!$t(s)&&(i=te(i),s=te(s)),!H(t)&&Re(i)&&!Re(s))return c?!1:(i.value=s,!0)}const r=H(t)&&ks(n)?Number(n)<t.length:ne(t,n),l=Reflect.set(t,n,s,Re(t)?t:o);return t===te(o)&&(r?At(s,i)&&ut(t,"set",n,s):ut(t,"add",n,s)),l}deleteProperty(t,n){const s=ne(t,n);t[n];const o=Reflect.deleteProperty(t,n);return o&&s&&ut(t,"delete",n,void 0),o}has(t,n){const s=Reflect.has(t,n);return(!gt(n)||!oi.has(n))&&$e(t,"has",n),s}ownKeys(t){return $e(t,"iterate",H(t)?"length":Nt),Reflect.ownKeys(t)}}class Pr extends ii{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Tr=new ri,Nr=new Pr,Or=new ri(!0);const gs=e=>e,xn=e=>Reflect.getPrototypeOf(e);function Ir(e,t,n){return function(...s){const o=this.__v_raw,i=te(o),r=Vt(i),l=e==="entries"||e===Symbol.iterator&&r,c=e==="keys"&&r,f=o[e](...s),a=n?gs:t?kn:Se;return!t&&$e(i,"iterate",c?ms:Nt),{next(){const{value:u,done:p}=f.next();return p?{value:u,done:p}:{value:l?[a(u[0]),a(u[1])]:a(u),done:p}},[Symbol.iterator](){return this}}}}function Cn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Mr(e,t){const n={get(o){const i=this.__v_raw,r=te(i),l=te(o);e||(At(o,l)&&$e(r,"get",o),$e(r,"get",l));const{has:c}=xn(r),f=t?gs:e?kn:Se;if(c.call(r,o))return f(i.get(o));if(c.call(r,l))return f(i.get(l));i!==r&&i.get(o)},get size(){const o=this.__v_raw;return!e&&$e(te(o),"iterate",Nt),Reflect.get(o,"size",o)},has(o){const i=this.__v_raw,r=te(i),l=te(o);return e||(At(o,l)&&$e(r,"has",o),$e(r,"has",l)),o===l?i.has(o):i.has(o)||i.has(l)},forEach(o,i){const r=this,l=r.__v_raw,c=te(l),f=t?gs:e?kn:Se;return!e&&$e(c,"iterate",Nt),l.forEach((a,u)=>o.call(i,f(a),f(u),r))}};return ke(n,e?{add:Cn("add"),set:Cn("set"),delete:Cn("delete"),clear:Cn("clear")}:{add(o){!t&&!Ve(o)&&!$t(o)&&(o=te(o));const i=te(this);return xn(i).has.call(i,o)||(i.add(o),ut(i,"add",o,o)),this},set(o,i){!t&&!Ve(i)&&!$t(i)&&(i=te(i));const r=te(this),{has:l,get:c}=xn(r);let f=l.call(r,o);f||(o=te(o),f=l.call(r,o));const a=c.call(r,o);return r.set(o,i),f?At(i,a)&&ut(r,"set",o,i):ut(r,"add",o,i),this},delete(o){const i=te(this),{has:r,get:l}=xn(i);let c=r.call(i,o);c||(o=te(o),c=r.call(i,o)),l&&l.call(i,o);const f=i.delete(o);return c&&ut(i,"delete",o,void 0),f},clear(){const o=te(this),i=o.size!==0,r=o.clear();return i&&ut(o,"clear",void 0,void 0),r}}),["keys","values","entries",Symbol.iterator].forEach(o=>{n[o]=Ir(o,e,t)}),n}function Is(e,t){const n=Mr(e,t);return(s,o,i)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?s:Reflect.get(ne(n,o)&&o in s?n:s,o,i)}const Dr={get:Is(!1,!1)},Fr={get:Is(!1,!0)},jr={get:Is(!0,!1)};const li=new WeakMap,ci=new WeakMap,ai=new WeakMap,Lr=new WeakMap;function Br(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Hr(e){return e.__v_skip||!Object.isExtensible(e)?0:Br(hr(e))}function Ot(e){return $t(e)?e:Ms(e,!1,Tr,Dr,li)}function ui(e){return Ms(e,!1,Or,Fr,ci)}function fi(e){return Ms(e,!0,Nr,jr,ai)}function Ms(e,t,n,s,o){if(!he(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=Hr(e);if(i===0)return e;const r=o.get(e);if(r)return r;const l=new Proxy(e,i===2?s:n);return o.set(e,l),l}function Ut(e){return $t(e)?Ut(e.__v_raw):!!(e&&e.__v_isReactive)}function $t(e){return!!(e&&e.__v_isReadonly)}function Ve(e){return!!(e&&e.__v_isShallow)}function Ds(e){return e?!!e.__v_raw:!1}function te(e){const t=e&&e.__v_raw;return t?te(t):e}function Vr(e){return!ne(e,"__v_skip")&&Object.isExtensible(e)&&Qo(e,"__v_skip",!0),e}const Se=e=>he(e)?Ot(e):e,kn=e=>he(e)?fi(e):e;function Re(e){return e?e.__v_isRef===!0:!1}function Z(e){return di(e,!1)}function Ur(e){return di(e,!0)}function di(e,t){return Re(e)?e:new Kr(e,t)}class Kr{constructor(t,n){this.dep=new Os,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:te(t),this._value=n?t:Se(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Ve(t)||$t(t);t=s?t:te(t),At(t,n)&&(this._rawValue=t,this._value=s?t:Se(t),this.dep.trigger())}}function A(e){return Re(e)?e.value:e}const qr={get:(e,t,n)=>t==="__v_raw"?e:A(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const o=e[t];return Re(o)&&!Re(n)?(o.value=n,!0):Reflect.set(e,t,n,s)}};function hi(e){return Ut(e)?e:new Proxy(e,qr)}class Qr{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Os(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=un-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&fe!==this)return Xo(this,!0),!0}get value(){const t=this.dep.track();return ti(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Wr(e,t,n=!1){let s,o;return q(e)?s=e:(s=e.get,o=e.set),new Qr(s,o,n)}const Sn={},Pn=new WeakMap;let Tt;function zr(e,t=!1,n=Tt){if(n){let s=Pn.get(n);s||Pn.set(n,s=[]),s.push(e)}}function Gr(e,t,n=ae){const{immediate:s,deep:o,once:i,scheduler:r,augmentJob:l,call:c}=n,f=I=>o?I:Ve(I)||o===!1||o===0?ft(I,1):ft(I);let a,u,p,m,x=!1,$=!1;if(Re(e)?(u=()=>e.value,x=Ve(e)):Ut(e)?(u=()=>f(e),x=!0):H(e)?($=!0,x=e.some(I=>Ut(I)||Ve(I)),u=()=>e.map(I=>{if(Re(I))return I.value;if(Ut(I))return f(I);if(q(I))return c?c(I,2):I()})):q(e)?t?u=c?()=>c(e,2):e:u=()=>{if(p){dt();try{p()}finally{ht()}}const I=Tt;Tt=a;try{return c?c(e,3,[m]):e(m)}finally{Tt=I}}:u=et,t&&o){const I=u,X=o===!0?1/0:o;u=()=>ft(I(),X)}const V=Cr(),F=()=>{a.stop(),V&&V.active&&Rs(V.effects,a)};if(i&&t){const I=t;t=(...X)=>{I(...X),F()}}let M=$?new Array(e.length).fill(Sn):Sn;const L=I=>{if(!(!(a.flags&1)||!a.dirty&&!I))if(t){const X=a.run();if(o||x||($?X.some((ve,ie)=>At(ve,M[ie])):At(X,M))){p&&p();const ve=Tt;Tt=a;try{const ie=[X,M===Sn?void 0:$&&M[0]===Sn?[]:M,m];M=X,c?c(t,3,ie):t(...ie)}finally{Tt=ve}}}else a.run()};return l&&l(L),a=new Jo(u),a.scheduler=r?()=>r(L,!1):L,m=I=>zr(I,!1,a),p=a.onStop=()=>{const I=Pn.get(a);if(I){if(c)c(I,4);else for(const X of I)X();Pn.delete(a)}},t?s?L(!0):M=a.run():r?r(L.bind(null,!0),!0):a.run(),F.pause=a.pause.bind(a),F.resume=a.resume.bind(a),F.stop=F,F}function ft(e,t=1/0,n){if(t<=0||!he(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Re(e))ft(e.value,t,n);else if(H(e))for(let s=0;s<e.length;s++)ft(e[s],t,n);else if(Vo(e)||Vt(e))e.forEach(s=>{ft(s,t,n)});else if(qo(e)){for(const s in e)ft(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&ft(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.15
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function yn(e,t,n,s){try{return s?e(...s):e()}catch(o){Un(o,t,n)}}function ot(e,t,n,s){if(q(e)){const o=yn(e,t,n,s);return o&&Uo(o)&&o.catch(i=>{Un(i,t,n)}),o}if(H(e)){const o=[];for(let i=0;i<e.length;i++)o.push(ot(e[i],t,n,s));return o}}function Un(e,t,n,s=!0){const o=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:r}=t&&t.appContext.config||ae;if(t){let l=t.parent;const c=t.proxy,f=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const a=l.ec;if(a){for(let u=0;u<a.length;u++)if(a[u](e,c,f)===!1)return}l=l.parent}if(i){dt(),yn(i,null,10,[e,c,f]),ht();return}}Jr(e,n,o,s,r)}function Jr(e,t,n,s=!0,o=!1){if(o)throw e;console.error(e)}const Te=[];let Xe=-1;const Kt=[];let xt=null,Ft=0;const pi=Promise.resolve();let Tn=null;function Fs(e){const t=Tn||pi;return e?t.then(this?e.bind(this):e):t}function Yr(e){let t=Xe+1,n=Te.length;for(;t<n;){const s=t+n>>>1,o=Te[s],i=dn(o);i<e||i===e&&o.flags&2?t=s+1:n=s}return t}function js(e){if(!(e.flags&1)){const t=dn(e),n=Te[Te.length-1];!n||!(e.flags&2)&&t>=dn(n)?Te.push(e):Te.splice(Yr(t),0,e),e.flags|=1,mi()}}function mi(){Tn||(Tn=pi.then(vi))}function Xr(e){H(e)?Kt.push(...e):xt&&e.id===-1?xt.splice(Ft+1,0,e):e.flags&1||(Kt.push(e),e.flags|=1),mi()}function Js(e,t,n=Xe+1){for(;n<Te.length;n++){const s=Te[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Te.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function gi(e){if(Kt.length){const t=[...new Set(Kt)].sort((n,s)=>dn(n)-dn(s));if(Kt.length=0,xt){xt.push(...t);return}for(xt=t,Ft=0;Ft<xt.length;Ft++){const n=xt[Ft];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}xt=null,Ft=0}}const dn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function vi(e){try{for(Xe=0;Xe<Te.length;Xe++){const t=Te[Xe];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),yn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Xe<Te.length;Xe++){const t=Te[Xe];t&&(t.flags&=-2)}Xe=-1,Te.length=0,gi(),Tn=null,(Te.length||Kt.length)&&vi()}}let Ae=null,_i=null;function Nn(e){const t=Ae;return Ae=e,_i=e&&e.type.__scopeId||null,t}function se(e,t=Ae,n){if(!t||e._n)return e;const s=(...o)=>{s._d&&lo(-1);const i=Nn(t);let r;try{r=e(...o)}finally{Nn(i),s._d&&lo(1)}return r};return s._n=!0,s._c=!0,s._d=!0,s}function tt(e,t){if(Ae===null)return e;const n=Wn(Ae),s=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[i,r,l,c=ae]=t[o];i&&(q(i)&&(i={mounted:i,updated:i}),i.deep&&ft(r),s.push({dir:i,instance:n,value:r,oldValue:void 0,arg:l,modifiers:c}))}return e}function kt(e,t,n,s){const o=e.dirs,i=t&&t.dirs;for(let r=0;r<o.length;r++){const l=o[r];i&&(l.oldValue=i[r].value);let c=l.dir[s];c&&(dt(),ot(c,n,8,[e.el,l,e,t]),ht())}}const Zr=Symbol("_vte"),el=e=>e.__isTeleport;function Ls(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ls(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function yi(e,t){return q(e)?ke({name:e.name},t,{setup:e}):e}function bi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function On(e,t,n,s,o=!1){if(H(e)){e.forEach((x,$)=>On(x,t&&(H(t)?t[$]:t),n,s,o));return}if(qt(s)&&!o){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&On(e,t,n,s.component.subTree);return}const i=s.shapeFlag&4?Wn(s.component):s.el,r=o?null:i,{i:l,r:c}=e,f=t&&t.r,a=l.refs===ae?l.refs={}:l.refs,u=l.setupState,p=te(u),m=u===ae?()=>!1:x=>ne(p,x);if(f!=null&&f!==c&&(be(f)?(a[f]=null,m(f)&&(u[f]=null)):Re(f)&&(f.value=null)),q(c))yn(c,l,12,[r,a]);else{const x=be(c),$=Re(c);if(x||$){const V=()=>{if(e.f){const F=x?m(c)?u[c]:a[c]:c.value;o?H(F)&&Rs(F,i):H(F)?F.includes(i)||F.push(i):x?(a[c]=[i],m(c)&&(u[c]=a[c])):(c.value=[i],e.k&&(a[e.k]=c.value))}else x?(a[c]=r,m(c)&&(u[c]=r)):$&&(c.value=r,e.k&&(a[e.k]=r))};r?(V.id=-1,je(V,n)):V()}}}Bn().requestIdleCallback;Bn().cancelIdleCallback;const qt=e=>!!e.type.__asyncLoader,wi=e=>e.type.__isKeepAlive;function tl(e,t){xi(e,"a",t)}function nl(e,t){xi(e,"da",t)}function xi(e,t,n=Ee){const s=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(Kn(t,s,n),n){let o=n.parent;for(;o&&o.parent;)wi(o.parent.vnode)&&sl(s,t,n,o),o=o.parent}}function sl(e,t,n,s){const o=Kn(t,e,s,!0);Si(()=>{Rs(s[t],o)},n)}function Kn(e,t,n=Ee,s=!1){if(n){const o=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...r)=>{dt();const l=bn(n),c=ot(t,n,e,r);return l(),ht(),c});return s?o.unshift(i):o.push(i),i}}const vt=e=>(t,n=Ee)=>{(!mn||e==="sp")&&Kn(e,(...s)=>t(...s),n)},ol=vt("bm"),Ci=vt("m"),il=vt("bu"),rl=vt("u"),ll=vt("bum"),Si=vt("um"),cl=vt("sp"),al=vt("rtg"),ul=vt("rtc");function fl(e,t=Ee){Kn("ec",e,t)}const dl="components";function Ys(e,t){return pl(dl,e,!0,t)||e}const hl=Symbol.for("v-ndc");function pl(e,t,n=!0,s=!1){const o=Ae||Ee;if(o){const i=o.type;{const l=ec(i,!1);if(l&&(l===t||l===Ke(t)||l===Ln(Ke(t))))return i}const r=Xs(o[e]||i[e],t)||Xs(o.appContext[e],t);return!r&&s?i:r}}function Xs(e,t){return e&&(e[t]||e[Ke(t)]||e[Ln(Ke(t))])}function Le(e,t,n,s){let o;const i=n,r=H(e);if(r||be(e)){const l=r&&Ut(e);let c=!1,f=!1;l&&(c=!Ve(e),f=$t(e),e=Vn(e)),o=new Array(e.length);for(let a=0,u=e.length;a<u;a++)o[a]=t(c?f?kn(Se(e[a])):Se(e[a]):e[a],a,void 0,i)}else if(typeof e=="number"){o=new Array(e);for(let l=0;l<e;l++)o[l]=t(l+1,l,void 0,i)}else if(he(e))if(e[Symbol.iterator])o=Array.from(e,(l,c)=>t(l,c,void 0,i));else{const l=Object.keys(e);o=new Array(l.length);for(let c=0,f=l.length;c<f;c++){const a=l[c];o[c]=t(e[a],a,c,i)}}else o=[];return o}function Bt(e,t,n={},s,o){if(Ae.ce||Ae.parent&&qt(Ae.parent)&&Ae.parent.ce)return t!=="default"&&(n.name=t),C(),me(de,null,[K("slot",n,s&&s())],64);let i=e[t];i&&i._c&&(i._d=!1),C();const r=i&&Ai(i(n)),l=n.key||r&&r.key,c=me(de,{key:(l&&!gt(l)?l:`_${t}`)+(!r&&s?"_fb":"")},r||(s?s():[]),r&&e._===1?64:-2);return i&&i._c&&(i._d=!0),c}function Ai(e){return e.some(t=>pn(t)?!(t.type===pt||t.type===de&&!Ai(t.children)):!0)?e:null}const vs=e=>e?qi(e)?Wn(e):vs(e.parent):null,rn=ke(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>vs(e.parent),$root:e=>vs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Ei(e),$forceUpdate:e=>e.f||(e.f=()=>{js(e.update)}),$nextTick:e=>e.n||(e.n=Fs.bind(e.proxy)),$watch:e=>Ml.bind(e)}),ns=(e,t)=>e!==ae&&!e.__isScriptSetup&&ne(e,t),ml={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:o,props:i,accessCache:r,type:l,appContext:c}=e;let f;if(t[0]!=="$"){const m=r[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return o[t];case 4:return n[t];case 3:return i[t]}else{if(ns(s,t))return r[t]=1,s[t];if(o!==ae&&ne(o,t))return r[t]=2,o[t];if((f=e.propsOptions[0])&&ne(f,t))return r[t]=3,i[t];if(n!==ae&&ne(n,t))return r[t]=4,n[t];_s&&(r[t]=0)}}const a=rn[t];let u,p;if(a)return t==="$attrs"&&$e(e.attrs,"get",""),a(e);if((u=l.__cssModules)&&(u=u[t]))return u;if(n!==ae&&ne(n,t))return r[t]=4,n[t];if(p=c.config.globalProperties,ne(p,t))return p[t]},set({_:e},t,n){const{data:s,setupState:o,ctx:i}=e;return ns(o,t)?(o[t]=n,!0):s!==ae&&ne(s,t)?(s[t]=n,!0):ne(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:o,propsOptions:i}},r){let l;return!!n[r]||e!==ae&&ne(e,r)||ns(t,r)||(l=i[0])&&ne(l,r)||ne(s,r)||ne(rn,r)||ne(o.config.globalProperties,r)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ne(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Zs(e){return H(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let _s=!0;function gl(e){const t=Ei(e),n=e.proxy,s=e.ctx;_s=!1,t.beforeCreate&&eo(t.beforeCreate,e,"bc");const{data:o,computed:i,methods:r,watch:l,provide:c,inject:f,created:a,beforeMount:u,mounted:p,beforeUpdate:m,updated:x,activated:$,deactivated:V,beforeDestroy:F,beforeUnmount:M,destroyed:L,unmounted:I,render:X,renderTracked:ve,renderTriggered:ie,errorCaptured:z,serverPrefetch:Y,expose:we,inheritAttrs:Ce,components:xe,directives:_e,filters:Rt}=t;if(f&&vl(f,s,null),r)for(const G in r){const W=r[G];q(W)&&(s[G]=W.bind(n))}if(o){const G=o.call(n,n);he(G)&&(e.data=Ot(G))}if(_s=!0,i)for(const G in i){const W=i[G],rt=q(W)?W.bind(n,n):q(W.get)?W.get.bind(n,n):et,bt=!q(W)&&q(W.set)?W.set.bind(n):et,We=re({get:rt,set:bt});Object.defineProperty(s,G,{enumerable:!0,configurable:!0,get:()=>We.value,set:Ne=>We.value=Ne})}if(l)for(const G in l)$i(l[G],s,n,G);if(c){const G=q(c)?c.call(n):c;Reflect.ownKeys(G).forEach(W=>{Wt(W,G[W])})}a&&eo(a,e,"c");function pe(G,W){H(W)?W.forEach(rt=>G(rt.bind(n))):W&&G(W.bind(n))}if(pe(ol,u),pe(Ci,p),pe(il,m),pe(rl,x),pe(tl,$),pe(nl,V),pe(fl,z),pe(ul,ve),pe(al,ie),pe(ll,M),pe(Si,I),pe(cl,Y),H(we))if(we.length){const G=e.exposed||(e.exposed={});we.forEach(W=>{Object.defineProperty(G,W,{get:()=>n[W],set:rt=>n[W]=rt})})}else e.exposed||(e.exposed={});X&&e.render===et&&(e.render=X),Ce!=null&&(e.inheritAttrs=Ce),xe&&(e.components=xe),_e&&(e.directives=_e),Y&&bi(e)}function vl(e,t,n=et){H(e)&&(e=ys(e));for(const s in e){const o=e[s];let i;he(o)?"default"in o?i=Ue(o.from||s,o.default,!0):i=Ue(o.from||s):i=Ue(o),Re(i)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>i.value,set:r=>i.value=r}):t[s]=i}}function eo(e,t,n){ot(H(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function $i(e,t,n,s){let o=s.includes(".")?Bi(n,s):()=>n[s];if(be(e)){const i=t[e];q(i)&&nt(o,i)}else if(q(e))nt(o,e.bind(n));else if(he(e))if(H(e))e.forEach(i=>$i(i,t,n,s));else{const i=q(e.handler)?e.handler.bind(n):t[e.handler];q(i)&&nt(o,i,e)}}function Ei(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:o,optionsCache:i,config:{optionMergeStrategies:r}}=e.appContext,l=i.get(t);let c;return l?c=l:!o.length&&!n&&!s?c=t:(c={},o.length&&o.forEach(f=>In(c,f,r,!0)),In(c,t,r)),he(t)&&i.set(t,c),c}function In(e,t,n,s=!1){const{mixins:o,extends:i}=t;i&&In(e,i,n,!0),o&&o.forEach(r=>In(e,r,n,!0));for(const r in t)if(!(s&&r==="expose")){const l=_l[r]||n&&n[r];e[r]=l?l(e[r],t[r]):t[r]}return e}const _l={data:to,props:no,emits:no,methods:tn,computed:tn,beforeCreate:Pe,created:Pe,beforeMount:Pe,mounted:Pe,beforeUpdate:Pe,updated:Pe,beforeDestroy:Pe,beforeUnmount:Pe,destroyed:Pe,unmounted:Pe,activated:Pe,deactivated:Pe,errorCaptured:Pe,serverPrefetch:Pe,components:tn,directives:tn,watch:bl,provide:to,inject:yl};function to(e,t){return t?e?function(){return ke(q(e)?e.call(this,this):e,q(t)?t.call(this,this):t)}:t:e}function yl(e,t){return tn(ys(e),ys(t))}function ys(e){if(H(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Pe(e,t){return e?[...new Set([].concat(e,t))]:t}function tn(e,t){return e?ke(Object.create(null),e,t):t}function no(e,t){return e?H(e)&&H(t)?[...new Set([...e,...t])]:ke(Object.create(null),Zs(e),Zs(t??{})):t}function bl(e,t){if(!e)return t;if(!t)return e;const n=ke(Object.create(null),e);for(const s in t)n[s]=Pe(e[s],t[s]);return n}function Ri(){return{app:null,config:{isNativeTag:fr,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let wl=0;function xl(e,t){return function(s,o=null){q(s)||(s=ke({},s)),o!=null&&!he(o)&&(o=null);const i=Ri(),r=new WeakSet,l=[];let c=!1;const f=i.app={_uid:wl++,_component:s,_props:o,_container:null,_context:i,_instance:null,version:nc,get config(){return i.config},set config(a){},use(a,...u){return r.has(a)||(a&&q(a.install)?(r.add(a),a.install(f,...u)):q(a)&&(r.add(a),a(f,...u))),f},mixin(a){return i.mixins.includes(a)||i.mixins.push(a),f},component(a,u){return u?(i.components[a]=u,f):i.components[a]},directive(a,u){return u?(i.directives[a]=u,f):i.directives[a]},mount(a,u,p){if(!c){const m=f._ceVNode||K(s,o);return m.appContext=i,p===!0?p="svg":p===!1&&(p=void 0),e(m,a,p),c=!0,f._container=a,a.__vue_app__=f,Wn(m.component)}},onUnmount(a){l.push(a)},unmount(){c&&(ot(l,f._instance,16),e(null,f._container),delete f._container.__vue_app__)},provide(a,u){return i.provides[a]=u,f},runWithContext(a){const u=Qt;Qt=f;try{return a()}finally{Qt=u}}};return f}}let Qt=null;function Wt(e,t){if(Ee){let n=Ee.provides;const s=Ee.parent&&Ee.parent.provides;s===n&&(n=Ee.provides=Object.create(s)),n[e]=t}}function Ue(e,t,n=!1){const s=Ee||Ae;if(s||Qt){let o=Qt?Qt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&q(t)?t.call(s&&s.proxy):t}}const ki={},Pi=()=>Object.create(ki),Ti=e=>Object.getPrototypeOf(e)===ki;function Cl(e,t,n,s=!1){const o={},i=Pi();e.propsDefaults=Object.create(null),Ni(e,t,o,i);for(const r in e.propsOptions[0])r in o||(o[r]=void 0);n?e.props=s?o:ui(o):e.type.props?e.props=o:e.props=i,e.attrs=i}function Sl(e,t,n,s){const{props:o,attrs:i,vnode:{patchFlag:r}}=e,l=te(o),[c]=e.propsOptions;let f=!1;if((s||r>0)&&!(r&16)){if(r&8){const a=e.vnode.dynamicProps;for(let u=0;u<a.length;u++){let p=a[u];if(qn(e.emitsOptions,p))continue;const m=t[p];if(c)if(ne(i,p))m!==i[p]&&(i[p]=m,f=!0);else{const x=Ke(p);o[x]=bs(c,l,x,m,e,!1)}else m!==i[p]&&(i[p]=m,f=!0)}}}else{Ni(e,t,o,i)&&(f=!0);let a;for(const u in l)(!t||!ne(t,u)&&((a=Et(u))===u||!ne(t,a)))&&(c?n&&(n[u]!==void 0||n[a]!==void 0)&&(o[u]=bs(c,l,u,void 0,e,!0)):delete o[u]);if(i!==l)for(const u in i)(!t||!ne(t,u))&&(delete i[u],f=!0)}f&&ut(e.attrs,"set","")}function Ni(e,t,n,s){const[o,i]=e.propsOptions;let r=!1,l;if(t)for(let c in t){if(nn(c))continue;const f=t[c];let a;o&&ne(o,a=Ke(c))?!i||!i.includes(a)?n[a]=f:(l||(l={}))[a]=f:qn(e.emitsOptions,c)||(!(c in s)||f!==s[c])&&(s[c]=f,r=!0)}if(i){const c=te(n),f=l||ae;for(let a=0;a<i.length;a++){const u=i[a];n[u]=bs(o,c,u,f[u],e,!ne(f,u))}}return r}function bs(e,t,n,s,o,i){const r=e[n];if(r!=null){const l=ne(r,"default");if(l&&s===void 0){const c=r.default;if(r.type!==Function&&!r.skipFactory&&q(c)){const{propsDefaults:f}=o;if(n in f)s=f[n];else{const a=bn(o);s=f[n]=c.call(null,t),a()}}else s=c;o.ce&&o.ce._setProp(n,s)}r[0]&&(i&&!l?s=!1:r[1]&&(s===""||s===Et(n))&&(s=!0))}return s}const Al=new WeakMap;function Oi(e,t,n=!1){const s=n?Al:t.propsCache,o=s.get(e);if(o)return o;const i=e.props,r={},l=[];let c=!1;if(!q(e)){const a=u=>{c=!0;const[p,m]=Oi(u,t,!0);ke(r,p),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!i&&!c)return he(e)&&s.set(e,Ht),Ht;if(H(i))for(let a=0;a<i.length;a++){const u=Ke(i[a]);so(u)&&(r[u]=ae)}else if(i)for(const a in i){const u=Ke(a);if(so(u)){const p=i[a],m=r[u]=H(p)||q(p)?{type:p}:ke({},p),x=m.type;let $=!1,V=!0;if(H(x))for(let F=0;F<x.length;++F){const M=x[F],L=q(M)&&M.name;if(L==="Boolean"){$=!0;break}else L==="String"&&(V=!1)}else $=q(x)&&x.name==="Boolean";m[0]=$,m[1]=V,($||ne(m,"default"))&&l.push(u)}}const f=[r,l];return he(e)&&s.set(e,f),f}function so(e){return e[0]!=="$"&&!nn(e)}const Bs=e=>e[0]==="_"||e==="$stable",Hs=e=>H(e)?e.map(Ze):[Ze(e)],$l=(e,t,n)=>{if(t._n)return t;const s=se((...o)=>Hs(t(...o)),n);return s._c=!1,s},Ii=(e,t,n)=>{const s=e._ctx;for(const o in e){if(Bs(o))continue;const i=e[o];if(q(i))t[o]=$l(o,i,s);else if(i!=null){const r=Hs(i);t[o]=()=>r}}},Mi=(e,t)=>{const n=Hs(t);e.slots.default=()=>n},Di=(e,t,n)=>{for(const s in t)(n||!Bs(s))&&(e[s]=t[s])},El=(e,t,n)=>{const s=e.slots=Pi();if(e.vnode.shapeFlag&32){const o=t._;o?(Di(s,t,n),n&&Qo(s,"_",o,!0)):Ii(t,s)}else t&&Mi(e,t)},Rl=(e,t,n)=>{const{vnode:s,slots:o}=e;let i=!0,r=ae;if(s.shapeFlag&32){const l=t._;l?n&&l===1?i=!1:Di(o,t,n):(i=!t.$stable,Ii(t,o)),r=t}else t&&(Mi(e,t),r={default:1});if(i)for(const l in o)!Bs(l)&&r[l]==null&&delete o[l]},je=Vl;function kl(e){return Pl(e)}function Pl(e,t){const n=Bn();n.__VUE__=!0;const{insert:s,remove:o,patchProp:i,createElement:r,createText:l,createComment:c,setText:f,setElementText:a,parentNode:u,nextSibling:p,setScopeId:m=et,insertStaticContent:x}=e,$=(d,h,g,_=null,w=null,b=null,k=void 0,R=null,E=!!h.dynamicChildren)=>{if(d===h)return;d&&!Zt(d,h)&&(_=y(d),Ne(d,w,b,!0),d=null),h.patchFlag===-2&&(E=!1,h.dynamicChildren=null);const{type:S,ref:B,shapeFlag:T}=h;switch(S){case Qn:V(d,h,g,_);break;case pt:F(d,h,g,_);break;case os:d==null&&M(h,g,_,k);break;case de:xe(d,h,g,_,w,b,k,R,E);break;default:T&1?X(d,h,g,_,w,b,k,R,E):T&6?_e(d,h,g,_,w,b,k,R,E):(T&64||T&128)&&S.process(d,h,g,_,w,b,k,R,E,D)}B!=null&&w&&On(B,d&&d.ref,b,h||d,!h)},V=(d,h,g,_)=>{if(d==null)s(h.el=l(h.children),g,_);else{const w=h.el=d.el;h.children!==d.children&&f(w,h.children)}},F=(d,h,g,_)=>{d==null?s(h.el=c(h.children||""),g,_):h.el=d.el},M=(d,h,g,_)=>{[d.el,d.anchor]=x(d.children,h,g,_,d.el,d.anchor)},L=({el:d,anchor:h},g,_)=>{let w;for(;d&&d!==h;)w=p(d),s(d,g,_),d=w;s(h,g,_)},I=({el:d,anchor:h})=>{let g;for(;d&&d!==h;)g=p(d),o(d),d=g;o(h)},X=(d,h,g,_,w,b,k,R,E)=>{h.type==="svg"?k="svg":h.type==="math"&&(k="mathml"),d==null?ve(h,g,_,w,b,k,R,E):Y(d,h,w,b,k,R,E)},ve=(d,h,g,_,w,b,k,R)=>{let E,S;const{props:B,shapeFlag:T,transition:j,dirs:U}=d;if(E=d.el=r(d.type,b,B&&B.is,B),T&8?a(E,d.children):T&16&&z(d.children,E,null,_,w,ss(d,b),k,R),U&&kt(d,null,_,"created"),ie(E,d,d.scopeId,k,_),B){for(const ue in B)ue!=="value"&&!nn(ue)&&i(E,ue,null,B[ue],b,_);"value"in B&&i(E,"value",null,B.value,b),(S=B.onVnodeBeforeMount)&&Ye(S,_,d)}U&&kt(d,null,_,"beforeMount");const J=Tl(w,j);J&&j.beforeEnter(E),s(E,h,g),((S=B&&B.onVnodeMounted)||J||U)&&je(()=>{S&&Ye(S,_,d),J&&j.enter(E),U&&kt(d,null,_,"mounted")},w)},ie=(d,h,g,_,w)=>{if(g&&m(d,g),_)for(let b=0;b<_.length;b++)m(d,_[b]);if(w){let b=w.subTree;if(h===b||Vi(b.type)&&(b.ssContent===h||b.ssFallback===h)){const k=w.vnode;ie(d,k,k.scopeId,k.slotScopeIds,w.parent)}}},z=(d,h,g,_,w,b,k,R,E=0)=>{for(let S=E;S<d.length;S++){const B=d[S]=R?Ct(d[S]):Ze(d[S]);$(null,B,h,g,_,w,b,k,R)}},Y=(d,h,g,_,w,b,k)=>{const R=h.el=d.el;let{patchFlag:E,dynamicChildren:S,dirs:B}=h;E|=d.patchFlag&16;const T=d.props||ae,j=h.props||ae;let U;if(g&&Pt(g,!1),(U=j.onVnodeBeforeUpdate)&&Ye(U,g,h,d),B&&kt(h,d,g,"beforeUpdate"),g&&Pt(g,!0),(T.innerHTML&&j.innerHTML==null||T.textContent&&j.textContent==null)&&a(R,""),S?we(d.dynamicChildren,S,R,g,_,ss(h,w),b):k||W(d,h,R,null,g,_,ss(h,w),b,!1),E>0){if(E&16)Ce(R,T,j,g,w);else if(E&2&&T.class!==j.class&&i(R,"class",null,j.class,w),E&4&&i(R,"style",T.style,j.style,w),E&8){const J=h.dynamicProps;for(let ue=0;ue<J.length;ue++){const oe=J[ue],De=T[oe],Oe=j[oe];(Oe!==De||oe==="value")&&i(R,oe,De,Oe,w,g)}}E&1&&d.children!==h.children&&a(R,h.children)}else!k&&S==null&&Ce(R,T,j,g,w);((U=j.onVnodeUpdated)||B)&&je(()=>{U&&Ye(U,g,h,d),B&&kt(h,d,g,"updated")},_)},we=(d,h,g,_,w,b,k)=>{for(let R=0;R<h.length;R++){const E=d[R],S=h[R],B=E.el&&(E.type===de||!Zt(E,S)||E.shapeFlag&198)?u(E.el):g;$(E,S,B,null,_,w,b,k,!0)}},Ce=(d,h,g,_,w)=>{if(h!==g){if(h!==ae)for(const b in h)!nn(b)&&!(b in g)&&i(d,b,h[b],null,w,_);for(const b in g){if(nn(b))continue;const k=g[b],R=h[b];k!==R&&b!=="value"&&i(d,b,R,k,w,_)}"value"in g&&i(d,"value",h.value,g.value,w)}},xe=(d,h,g,_,w,b,k,R,E)=>{const S=h.el=d?d.el:l(""),B=h.anchor=d?d.anchor:l("");let{patchFlag:T,dynamicChildren:j,slotScopeIds:U}=h;U&&(R=R?R.concat(U):U),d==null?(s(S,g,_),s(B,g,_),z(h.children||[],g,B,w,b,k,R,E)):T>0&&T&64&&j&&d.dynamicChildren?(we(d.dynamicChildren,j,g,w,b,k,R),(h.key!=null||w&&h===w.subTree)&&Fi(d,h,!0)):W(d,h,g,B,w,b,k,R,E)},_e=(d,h,g,_,w,b,k,R,E)=>{h.slotScopeIds=R,d==null?h.shapeFlag&512?w.ctx.activate(h,g,_,k,E):Rt(h,g,_,w,b,k,E):yt(d,h,E)},Rt=(d,h,g,_,w,b,k)=>{const R=d.component=Gl(d,_,w);if(wi(d)&&(R.ctx.renderer=D),Jl(R,!1,k),R.asyncDep){if(w&&w.registerDep(R,pe,k),!d.el){const E=R.subTree=K(pt);F(null,E,h,g)}}else pe(R,d,h,g,w,b,k)},yt=(d,h,g)=>{const _=h.component=d.component;if(Bl(d,h,g))if(_.asyncDep&&!_.asyncResolved){G(_,h,g);return}else _.next=h,_.update();else h.el=d.el,_.vnode=h},pe=(d,h,g,_,w,b,k)=>{const R=()=>{if(d.isMounted){let{next:T,bu:j,u:U,parent:J,vnode:ue}=d;{const Ge=ji(d);if(Ge){T&&(T.el=ue.el,G(d,T,k)),Ge.asyncDep.then(()=>{d.isUnmounted||R()});return}}let oe=T,De;Pt(d,!1),T?(T.el=ue.el,G(d,T,k)):T=ue,j&&$n(j),(De=T.props&&T.props.onVnodeBeforeUpdate)&&Ye(De,J,T,ue),Pt(d,!0);const Oe=io(d),ze=d.subTree;d.subTree=Oe,$(ze,Oe,u(ze.el),y(ze),d,w,b),T.el=Oe.el,oe===null&&Hl(d,Oe.el),U&&je(U,w),(De=T.props&&T.props.onVnodeUpdated)&&je(()=>Ye(De,J,T,ue),w)}else{let T;const{el:j,props:U}=h,{bm:J,m:ue,parent:oe,root:De,type:Oe}=d,ze=qt(h);Pt(d,!1),J&&$n(J),!ze&&(T=U&&U.onVnodeBeforeMount)&&Ye(T,oe,h),Pt(d,!0);{De.ce&&De.ce._injectChildStyle(Oe);const Ge=d.subTree=io(d);$(null,Ge,g,_,d,w,b),h.el=Ge.el}if(ue&&je(ue,w),!ze&&(T=U&&U.onVnodeMounted)){const Ge=h;je(()=>Ye(T,oe,Ge),w)}(h.shapeFlag&256||oe&&qt(oe.vnode)&&oe.vnode.shapeFlag&256)&&d.a&&je(d.a,w),d.isMounted=!0,h=g=_=null}};d.scope.on();const E=d.effect=new Jo(R);d.scope.off();const S=d.update=E.run.bind(E),B=d.job=E.runIfDirty.bind(E);B.i=d,B.id=d.uid,E.scheduler=()=>js(B),Pt(d,!0),S()},G=(d,h,g)=>{h.component=d;const _=d.vnode.props;d.vnode=h,d.next=null,Sl(d,h.props,_,g),Rl(d,h.children,g),dt(),Js(d),ht()},W=(d,h,g,_,w,b,k,R,E=!1)=>{const S=d&&d.children,B=d?d.shapeFlag:0,T=h.children,{patchFlag:j,shapeFlag:U}=h;if(j>0){if(j&128){bt(S,T,g,_,w,b,k,R,E);return}else if(j&256){rt(S,T,g,_,w,b,k,R,E);return}}U&8?(B&16&&He(S,w,b),T!==S&&a(g,T)):B&16?U&16?bt(S,T,g,_,w,b,k,R,E):He(S,w,b,!0):(B&8&&a(g,""),U&16&&z(T,g,_,w,b,k,R,E))},rt=(d,h,g,_,w,b,k,R,E)=>{d=d||Ht,h=h||Ht;const S=d.length,B=h.length,T=Math.min(S,B);let j;for(j=0;j<T;j++){const U=h[j]=E?Ct(h[j]):Ze(h[j]);$(d[j],U,g,null,w,b,k,R,E)}S>B?He(d,w,b,!0,!1,T):z(h,g,_,w,b,k,R,E,T)},bt=(d,h,g,_,w,b,k,R,E)=>{let S=0;const B=h.length;let T=d.length-1,j=B-1;for(;S<=T&&S<=j;){const U=d[S],J=h[S]=E?Ct(h[S]):Ze(h[S]);if(Zt(U,J))$(U,J,g,null,w,b,k,R,E);else break;S++}for(;S<=T&&S<=j;){const U=d[T],J=h[j]=E?Ct(h[j]):Ze(h[j]);if(Zt(U,J))$(U,J,g,null,w,b,k,R,E);else break;T--,j--}if(S>T){if(S<=j){const U=j+1,J=U<B?h[U].el:_;for(;S<=j;)$(null,h[S]=E?Ct(h[S]):Ze(h[S]),g,J,w,b,k,R,E),S++}}else if(S>j)for(;S<=T;)Ne(d[S],w,b,!0),S++;else{const U=S,J=S,ue=new Map;for(S=J;S<=j;S++){const Fe=h[S]=E?Ct(h[S]):Ze(h[S]);Fe.key!=null&&ue.set(Fe.key,S)}let oe,De=0;const Oe=j-J+1;let ze=!1,Ge=0;const Yt=new Array(Oe);for(S=0;S<Oe;S++)Yt[S]=0;for(S=U;S<=T;S++){const Fe=d[S];if(De>=Oe){Ne(Fe,w,b,!0);continue}let Je;if(Fe.key!=null)Je=ue.get(Fe.key);else for(oe=J;oe<=j;oe++)if(Yt[oe-J]===0&&Zt(Fe,h[oe])){Je=oe;break}Je===void 0?Ne(Fe,w,b,!0):(Yt[Je-J]=S+1,Je>=Ge?Ge=Je:ze=!0,$(Fe,h[Je],g,null,w,b,k,R,E),De++)}const qs=ze?Nl(Yt):Ht;for(oe=qs.length-1,S=Oe-1;S>=0;S--){const Fe=J+S,Je=h[Fe],Qs=Fe+1<B?h[Fe+1].el:_;Yt[S]===0?$(null,Je,g,Qs,w,b,k,R,E):ze&&(oe<0||S!==qs[oe]?We(Je,g,Qs,2):oe--)}}},We=(d,h,g,_,w=null)=>{const{el:b,type:k,transition:R,children:E,shapeFlag:S}=d;if(S&6){We(d.component.subTree,h,g,_);return}if(S&128){d.suspense.move(h,g,_);return}if(S&64){k.move(d,h,g,D);return}if(k===de){s(b,h,g);for(let T=0;T<E.length;T++)We(E[T],h,g,_);s(d.anchor,h,g);return}if(k===os){L(d,h,g);return}if(_!==2&&S&1&&R)if(_===0)R.beforeEnter(b),s(b,h,g),je(()=>R.enter(b),w);else{const{leave:T,delayLeave:j,afterLeave:U}=R,J=()=>{d.ctx.isUnmounted?o(b):s(b,h,g)},ue=()=>{T(b,()=>{J(),U&&U()})};j?j(b,J,ue):ue()}else s(b,h,g)},Ne=(d,h,g,_=!1,w=!1)=>{const{type:b,props:k,ref:R,children:E,dynamicChildren:S,shapeFlag:B,patchFlag:T,dirs:j,cacheIndex:U}=d;if(T===-2&&(w=!1),R!=null&&(dt(),On(R,null,g,d,!0),ht()),U!=null&&(h.renderCache[U]=void 0),B&256){h.ctx.deactivate(d);return}const J=B&1&&j,ue=!qt(d);let oe;if(ue&&(oe=k&&k.onVnodeBeforeUnmount)&&Ye(oe,h,d),B&6)wn(d.component,g,_);else{if(B&128){d.suspense.unmount(g,_);return}J&&kt(d,null,h,"beforeUnmount"),B&64?d.type.remove(d,h,g,D,_):S&&!S.hasOnce&&(b!==de||T>0&&T&64)?He(S,h,g,!1,!0):(b===de&&T&384||!w&&B&16)&&He(E,h,g),_&&It(d)}(ue&&(oe=k&&k.onVnodeUnmounted)||J)&&je(()=>{oe&&Ye(oe,h,d),J&&kt(d,null,h,"unmounted")},g)},It=d=>{const{type:h,el:g,anchor:_,transition:w}=d;if(h===de){Mt(g,_);return}if(h===os){I(d);return}const b=()=>{o(g),w&&!w.persisted&&w.afterLeave&&w.afterLeave()};if(d.shapeFlag&1&&w&&!w.persisted){const{leave:k,delayLeave:R}=w,E=()=>k(g,b);R?R(d.el,b,E):E()}else b()},Mt=(d,h)=>{let g;for(;d!==h;)g=p(d),o(d),d=g;o(h)},wn=(d,h,g)=>{const{bum:_,scope:w,job:b,subTree:k,um:R,m:E,a:S,parent:B,slots:{__:T}}=d;oo(E),oo(S),_&&$n(_),B&&H(T)&&T.forEach(j=>{B.renderCache[j]=void 0}),w.stop(),b&&(b.flags|=8,Ne(k,d,h,g)),R&&je(R,h),je(()=>{d.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&d.asyncDep&&!d.asyncResolved&&d.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},He=(d,h,g,_=!1,w=!1,b=0)=>{for(let k=b;k<d.length;k++)Ne(d[k],h,g,_,w)},y=d=>{if(d.shapeFlag&6)return y(d.component.subTree);if(d.shapeFlag&128)return d.suspense.next();const h=p(d.anchor||d.el),g=h&&h[Zr];return g?p(g):h};let O=!1;const P=(d,h,g)=>{d==null?h._vnode&&Ne(h._vnode,null,null,!0):$(h._vnode||null,d,h,null,null,null,g),h._vnode=d,O||(O=!0,Js(),gi(),O=!1)},D={p:$,um:Ne,m:We,r:It,mt:Rt,mc:z,pc:W,pbc:we,n:y,o:e};return{render:P,hydrate:void 0,createApp:xl(P)}}function ss({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Pt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Tl(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Fi(e,t,n=!1){const s=e.children,o=t.children;if(H(s)&&H(o))for(let i=0;i<s.length;i++){const r=s[i];let l=o[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=o[i]=Ct(o[i]),l.el=r.el),!n&&l.patchFlag!==-2&&Fi(r,l)),l.type===Qn&&(l.el=r.el),l.type===pt&&!l.el&&(l.el=r.el)}}function Nl(e){const t=e.slice(),n=[0];let s,o,i,r,l;const c=e.length;for(s=0;s<c;s++){const f=e[s];if(f!==0){if(o=n[n.length-1],e[o]<f){t[s]=o,n.push(s);continue}for(i=0,r=n.length-1;i<r;)l=i+r>>1,e[n[l]]<f?i=l+1:r=l;f<e[n[i]]&&(i>0&&(t[s]=n[i-1]),n[i]=s)}}for(i=n.length,r=n[i-1];i-- >0;)n[i]=r,r=t[r];return n}function ji(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ji(t)}function oo(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Ol=Symbol.for("v-scx"),Il=()=>Ue(Ol);function nt(e,t,n){return Li(e,t,n)}function Li(e,t,n=ae){const{immediate:s,deep:o,flush:i,once:r}=n,l=ke({},n),c=t&&s||!t&&i!=="post";let f;if(mn){if(i==="sync"){const m=Il();f=m.__watcherHandles||(m.__watcherHandles=[])}else if(!c){const m=()=>{};return m.stop=et,m.resume=et,m.pause=et,m}}const a=Ee;l.call=(m,x,$)=>ot(m,a,x,$);let u=!1;i==="post"?l.scheduler=m=>{je(m,a&&a.suspense)}:i!=="sync"&&(u=!0,l.scheduler=(m,x)=>{x?m():js(m)}),l.augmentJob=m=>{t&&(m.flags|=4),u&&(m.flags|=2,a&&(m.id=a.uid,m.i=a))};const p=Gr(e,t,l);return mn&&(f?f.push(p):c&&p()),p}function Ml(e,t,n){const s=this.proxy,o=be(e)?e.includes(".")?Bi(s,e):()=>s[e]:e.bind(s,s);let i;q(t)?i=t:(i=t.handler,n=t);const r=bn(this),l=Li(o,i.bind(s),n);return r(),l}function Bi(e,t){const n=t.split(".");return()=>{let s=e;for(let o=0;o<n.length&&s;o++)s=s[n[o]];return s}}const Dl=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ke(t)}Modifiers`]||e[`${Et(t)}Modifiers`];function Fl(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||ae;let o=n;const i=t.startsWith("update:"),r=i&&Dl(s,t.slice(7));r&&(r.trim&&(o=n.map(a=>be(a)?a.trim():a)),r.number&&(o=n.map(ds)));let l,c=s[l=Yn(t)]||s[l=Yn(Ke(t))];!c&&i&&(c=s[l=Yn(Et(t))]),c&&ot(c,e,6,o);const f=s[l+"Once"];if(f){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,ot(f,e,6,o)}}function Hi(e,t,n=!1){const s=t.emitsCache,o=s.get(e);if(o!==void 0)return o;const i=e.emits;let r={},l=!1;if(!q(e)){const c=f=>{const a=Hi(f,t,!0);a&&(l=!0,ke(r,a))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!i&&!l?(he(e)&&s.set(e,null),null):(H(i)?i.forEach(c=>r[c]=null):ke(r,i),he(e)&&s.set(e,r),r)}function qn(e,t){return!e||!Dn(t)?!1:(t=t.slice(2).replace(/Once$/,""),ne(e,t[0].toLowerCase()+t.slice(1))||ne(e,Et(t))||ne(e,t))}function io(e){const{type:t,vnode:n,proxy:s,withProxy:o,propsOptions:[i],slots:r,attrs:l,emit:c,render:f,renderCache:a,props:u,data:p,setupState:m,ctx:x,inheritAttrs:$}=e,V=Nn(e);let F,M;try{if(n.shapeFlag&4){const I=o||s,X=I;F=Ze(f.call(X,I,a,u,m,p,x)),M=l}else{const I=t;F=Ze(I.length>1?I(u,{attrs:l,slots:r,emit:c}):I(u,null)),M=t.props?l:jl(l)}}catch(I){ln.length=0,Un(I,e,1),F=K(pt)}let L=F;if(M&&$!==!1){const I=Object.keys(M),{shapeFlag:X}=L;I.length&&X&7&&(i&&I.some(Es)&&(M=Ll(M,i)),L=zt(L,M,!1,!0))}return n.dirs&&(L=zt(L,null,!1,!0),L.dirs=L.dirs?L.dirs.concat(n.dirs):n.dirs),n.transition&&Ls(L,n.transition),F=L,Nn(V),F}const jl=e=>{let t;for(const n in e)(n==="class"||n==="style"||Dn(n))&&((t||(t={}))[n]=e[n]);return t},Ll=(e,t)=>{const n={};for(const s in e)(!Es(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Bl(e,t,n){const{props:s,children:o,component:i}=e,{props:r,children:l,patchFlag:c}=t,f=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?ro(s,r,f):!!r;if(c&8){const a=t.dynamicProps;for(let u=0;u<a.length;u++){const p=a[u];if(r[p]!==s[p]&&!qn(f,p))return!0}}}else return(o||l)&&(!l||!l.$stable)?!0:s===r?!1:s?r?ro(s,r,f):!0:!!r;return!1}function ro(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let o=0;o<s.length;o++){const i=s[o];if(t[i]!==e[i]&&!qn(n,i))return!0}return!1}function Hl({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Vi=e=>e.__isSuspense;function Vl(e,t){t&&t.pendingBranch?H(e)?t.effects.push(...e):t.effects.push(e):Xr(e)}const de=Symbol.for("v-fgt"),Qn=Symbol.for("v-txt"),pt=Symbol.for("v-cmt"),os=Symbol.for("v-stc"),ln=[];let Be=null;function C(e=!1){ln.push(Be=e?null:[])}function Ul(){ln.pop(),Be=ln[ln.length-1]||null}let hn=1;function lo(e,t=!1){hn+=e,e<0&&Be&&t&&(Be.hasOnce=!0)}function Ui(e){return e.dynamicChildren=hn>0?Be||Ht:null,Ul(),hn>0&&Be&&Be.push(e),e}function N(e,t,n,s,o,i){return Ui(v(e,t,n,s,o,i,!0))}function me(e,t,n,s,o){return Ui(K(e,t,n,s,o,!0))}function pn(e){return e?e.__v_isVNode===!0:!1}function Zt(e,t){return e.type===t.type&&e.key===t.key}const Ki=({key:e})=>e??null,En=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?be(e)||Re(e)||q(e)?{i:Ae,r:e,k:t,f:!!n}:e:null);function v(e,t=null,n=null,s=0,o=null,i=e===de?0:1,r=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ki(t),ref:t&&En(t),scopeId:_i,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:s,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:Ae};return l?(Vs(c,n),i&128&&e.normalize(c)):n&&(c.shapeFlag|=be(n)?8:16),hn>0&&!r&&Be&&(c.patchFlag>0||i&6)&&c.patchFlag!==32&&Be.push(c),c}const K=Kl;function Kl(e,t=null,n=null,s=0,o=null,i=!1){if((!e||e===hl)&&(e=pt),pn(e)){const l=zt(e,t,!0);return n&&Vs(l,n),hn>0&&!i&&Be&&(l.shapeFlag&6?Be[Be.indexOf(e)]=l:Be.push(l)),l.patchFlag=-2,l}if(tc(e)&&(e=e.__vccOpts),t){t=ql(t);let{class:l,style:c}=t;l&&!be(l)&&(t.class=Me(l)),he(c)&&(Ds(c)&&!H(c)&&(c=ke({},c)),t.style=Hn(c))}const r=be(e)?1:Vi(e)?128:el(e)?64:he(e)?4:q(e)?2:0;return v(e,t,n,s,o,r,i,!0)}function ql(e){return e?Ds(e)||Ti(e)?ke({},e):e:null}function zt(e,t,n=!1,s=!1){const{props:o,ref:i,patchFlag:r,children:l,transition:c}=e,f=t?Ql(o||{},t):o,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:f,key:f&&Ki(f),ref:t&&t.ref?n&&i?H(i)?i.concat(En(t)):[i,En(t)]:En(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==de?r===-1?16:r|16:r,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&zt(e.ssContent),ssFallback:e.ssFallback&&zt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&Ls(a,c.clone(a)),a}function ye(e=" ",t=0){return K(Qn,null,e,t)}function le(e="",t=!1){return t?(C(),me(pt,null,e)):K(pt,null,e)}function Ze(e){return e==null||typeof e=="boolean"?K(pt):H(e)?K(de,null,e.slice()):pn(e)?Ct(e):K(Qn,null,String(e))}function Ct(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:zt(e)}function Vs(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(H(t))n=16;else if(typeof t=="object")if(s&65){const o=t.default;o&&(o._c&&(o._d=!1),Vs(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!Ti(t)?t._ctx=Ae:o===3&&Ae&&(Ae.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else q(t)?(t={default:t,_ctx:Ae},n=32):(t=String(t),s&64?(n=16,t=[ye(t)]):n=8);e.children=t,e.shapeFlag|=n}function Ql(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const o in s)if(o==="class")t.class!==s.class&&(t.class=Me([t.class,s.class]));else if(o==="style")t.style=Hn([t.style,s.style]);else if(Dn(o)){const i=t[o],r=s[o];r&&i!==r&&!(H(i)&&i.includes(r))&&(t[o]=i?[].concat(i,r):r)}else o!==""&&(t[o]=s[o])}return t}function Ye(e,t,n,s=null){ot(e,t,7,[n,s])}const Wl=Ri();let zl=0;function Gl(e,t,n){const s=e.type,o=(t?t.appContext:e.appContext)||Wl,i={uid:zl++,vnode:e,type:s,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new xr(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Oi(s,o),emitsOptions:Hi(s,o),emit:null,emitted:null,propsDefaults:ae,inheritAttrs:s.inheritAttrs,ctx:ae,data:ae,props:ae,attrs:ae,slots:ae,refs:ae,setupState:ae,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=Fl.bind(null,i),e.ce&&e.ce(i),i}let Ee=null,Mn,ws;{const e=Bn(),t=(n,s)=>{let o;return(o=e[n])||(o=e[n]=[]),o.push(s),i=>{o.length>1?o.forEach(r=>r(i)):o[0](i)}};Mn=t("__VUE_INSTANCE_SETTERS__",n=>Ee=n),ws=t("__VUE_SSR_SETTERS__",n=>mn=n)}const bn=e=>{const t=Ee;return Mn(e),e.scope.on(),()=>{e.scope.off(),Mn(t)}},co=()=>{Ee&&Ee.scope.off(),Mn(null)};function qi(e){return e.vnode.shapeFlag&4}let mn=!1;function Jl(e,t=!1,n=!1){t&&ws(t);const{props:s,children:o}=e.vnode,i=qi(e);Cl(e,s,i,t),El(e,o,n||t);const r=i?Yl(e,t):void 0;return t&&ws(!1),r}function Yl(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,ml);const{setup:s}=n;if(s){dt();const o=e.setupContext=s.length>1?Zl(e):null,i=bn(e),r=yn(s,e,0,[e.props,o]),l=Uo(r);if(ht(),i(),(l||e.sp)&&!qt(e)&&bi(e),l){if(r.then(co,co),t)return r.then(c=>{ao(e,c)}).catch(c=>{Un(c,e,0)});e.asyncDep=r}else ao(e,r)}else Qi(e)}function ao(e,t,n){q(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:he(t)&&(e.setupState=hi(t)),Qi(e)}function Qi(e,t,n){const s=e.type;e.render||(e.render=s.render||et);{const o=bn(e);dt();try{gl(e)}finally{ht(),o()}}}const Xl={get(e,t){return $e(e,"get",""),e[t]}};function Zl(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Xl),slots:e.slots,emit:e.emit,expose:t}}function Wn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(hi(Vr(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in rn)return rn[n](e)},has(t,n){return n in t||n in rn}})):e.proxy}function ec(e,t=!0){return q(e)?e.displayName||e.name:e.name||t&&e.__name}function tc(e){return q(e)&&"__vccOpts"in e}const re=(e,t)=>Wr(e,t,mn);function Wi(e,t,n){const s=arguments.length;return s===2?he(t)&&!H(t)?pn(t)?K(e,null,[t]):K(e,t):K(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&pn(n)&&(n=[n]),K(e,t,n))}const nc="3.5.15";/**
* @vue/runtime-dom v3.5.15
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let xs;const uo=typeof window<"u"&&window.trustedTypes;if(uo)try{xs=uo.createPolicy("vue",{createHTML:e=>e})}catch{}const zi=xs?e=>xs.createHTML(e):e=>e,sc="http://www.w3.org/2000/svg",oc="http://www.w3.org/1998/Math/MathML",at=typeof document<"u"?document:null,fo=at&&at.createElement("template"),ic={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const o=t==="svg"?at.createElementNS(sc,e):t==="mathml"?at.createElementNS(oc,e):n?at.createElement(e,{is:n}):at.createElement(e);return e==="select"&&s&&s.multiple!=null&&o.setAttribute("multiple",s.multiple),o},createText:e=>at.createTextNode(e),createComment:e=>at.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>at.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,o,i){const r=n?n.previousSibling:t.lastChild;if(o&&(o===i||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),!(o===i||!(o=o.nextSibling)););else{fo.innerHTML=zi(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=fo.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[r?r.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},rc=Symbol("_vtc");function lc(e,t,n){const s=e[rc];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const ho=Symbol("_vod"),cc=Symbol("_vsh"),ac=Symbol(""),uc=/(^|;)\s*display\s*:/;function fc(e,t,n){const s=e.style,o=be(n);let i=!1;if(n&&!o){if(t)if(be(t))for(const r of t.split(";")){const l=r.slice(0,r.indexOf(":")).trim();n[l]==null&&Rn(s,l,"")}else for(const r in t)n[r]==null&&Rn(s,r,"");for(const r in n)r==="display"&&(i=!0),Rn(s,r,n[r])}else if(o){if(t!==n){const r=s[ac];r&&(n+=";"+r),s.cssText=n,i=uc.test(n)}}else t&&e.removeAttribute("style");ho in e&&(e[ho]=i?s.display:"",e[cc]&&(s.display="none"))}const po=/\s*!important$/;function Rn(e,t,n){if(H(n))n.forEach(s=>Rn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=dc(e,t);po.test(n)?e.setProperty(Et(s),n.replace(po,""),"important"):e[s]=n}}const mo=["Webkit","Moz","ms"],is={};function dc(e,t){const n=is[t];if(n)return n;let s=Ke(t);if(s!=="filter"&&s in e)return is[t]=s;s=Ln(s);for(let o=0;o<mo.length;o++){const i=mo[o]+s;if(i in e)return is[t]=i}return t}const go="http://www.w3.org/1999/xlink";function vo(e,t,n,s,o,i=wr(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(go,t.slice(6,t.length)):e.setAttributeNS(go,t,n):n==null||i&&!Wo(n)?e.removeAttribute(t):e.setAttribute(t,i?"":gt(n)?String(n):n)}function _o(e,t,n,s,o){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?zi(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const l=i==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let r=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Wo(n):n==null&&l==="string"?(n="",r=!0):l==="number"&&(n=0,r=!0)}try{e[t]=n}catch{}r&&e.removeAttribute(o||t)}function jt(e,t,n,s){e.addEventListener(t,n,s)}function hc(e,t,n,s){e.removeEventListener(t,n,s)}const yo=Symbol("_vei");function pc(e,t,n,s,o=null){const i=e[yo]||(e[yo]={}),r=i[t];if(s&&r)r.value=s;else{const[l,c]=mc(t);if(s){const f=i[t]=_c(s,o);jt(e,l,f,c)}else r&&(hc(e,l,r,c),i[t]=void 0)}}const bo=/(?:Once|Passive|Capture)$/;function mc(e){let t;if(bo.test(e)){t={};let s;for(;s=e.match(bo);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Et(e.slice(2)),t]}let rs=0;const gc=Promise.resolve(),vc=()=>rs||(gc.then(()=>rs=0),rs=Date.now());function _c(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;ot(yc(s,n.value),t,5,[s])};return n.value=e,n.attached=vc(),n}function yc(e,t){if(H(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>o=>!o._stopped&&s&&s(o))}else return t}const wo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,bc=(e,t,n,s,o,i)=>{const r=o==="svg";t==="class"?lc(e,s,r):t==="style"?fc(e,n,s):Dn(t)?Es(t)||pc(e,t,n,s,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):wc(e,t,s,r))?(_o(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&vo(e,t,s,r,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!be(s))?_o(e,Ke(t),s,i,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),vo(e,t,s,r))};function wc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&wo(t)&&q(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return wo(t)&&be(n)?!1:t in e}const xo=e=>{const t=e.props["onUpdate:modelValue"]||!1;return H(t)?n=>$n(t,n):t};function xc(e){e.target.composing=!0}function Co(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const ls=Symbol("_assign"),st={created(e,{modifiers:{lazy:t,trim:n,number:s}},o){e[ls]=xo(o);const i=s||o.props&&o.props.type==="number";jt(e,t?"change":"input",r=>{if(r.target.composing)return;let l=e.value;n&&(l=l.trim()),i&&(l=ds(l)),e[ls](l)}),n&&jt(e,"change",()=>{e.value=e.value.trim()}),t||(jt(e,"compositionstart",xc),jt(e,"compositionend",Co),jt(e,"change",Co))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:o,number:i}},r){if(e[ls]=xo(r),e.composing)return;const l=(i||e.type==="number")&&!/^0\d/.test(e.value)?ds(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||o&&e.value.trim()===c)||(e.value=c))}},Cc=["ctrl","shift","alt","meta"],Sc={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Cc.some(n=>e[`${n}Key`]&&!t.includes(n))},mt=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(o,...i)=>{for(let r=0;r<t.length;r++){const l=Sc[t[r]];if(l&&l(o,t))return}return e(o,...i)})},Ac={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},gn=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=o=>{if(!("key"in o))return;const i=Et(o.key);if(t.some(r=>r===i||Ac[r]===i))return e(o)})},$c=ke({patchProp:bc},ic);let So;function Ec(){return So||(So=kl($c))}const Rc=(...e)=>{const t=Ec().createApp(...e),{mount:n}=t;return t.mount=s=>{const o=Pc(s);if(!o)return;const i=t._component;!q(i)&&!i.render&&!i.template&&(i.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const r=n(o,!1,kc(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),r},t};function kc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Pc(e){return be(e)?document.querySelector(e):e}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Lt=typeof document<"u";function Gi(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Tc(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Gi(e.default)}const ee=Object.assign;function cs(e,t){const n={};for(const s in t){const o=t[s];n[s]=Qe(o)?o.map(e):e(o)}return n}const cn=()=>{},Qe=Array.isArray,Ji=/#/g,Nc=/&/g,Oc=/\//g,Ic=/=/g,Mc=/\?/g,Yi=/\+/g,Dc=/%5B/g,Fc=/%5D/g,Xi=/%5E/g,jc=/%60/g,Zi=/%7B/g,Lc=/%7C/g,er=/%7D/g,Bc=/%20/g;function Us(e){return encodeURI(""+e).replace(Lc,"|").replace(Dc,"[").replace(Fc,"]")}function Hc(e){return Us(e).replace(Zi,"{").replace(er,"}").replace(Xi,"^")}function Cs(e){return Us(e).replace(Yi,"%2B").replace(Bc,"+").replace(Ji,"%23").replace(Nc,"%26").replace(jc,"`").replace(Zi,"{").replace(er,"}").replace(Xi,"^")}function Vc(e){return Cs(e).replace(Ic,"%3D")}function Uc(e){return Us(e).replace(Ji,"%23").replace(Mc,"%3F")}function Kc(e){return e==null?"":Uc(e).replace(Oc,"%2F")}function vn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const qc=/\/$/,Qc=e=>e.replace(qc,"");function as(e,t,n="/"){let s,o={},i="",r="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(s=t.slice(0,c),i=t.slice(c+1,l>-1?l:t.length),o=e(i)),l>-1&&(s=s||t.slice(0,l),r=t.slice(l,t.length)),s=Jc(s??t,n),{fullPath:s+(i&&"?")+i+r,path:s,query:o,hash:vn(r)}}function Wc(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Ao(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function zc(e,t,n){const s=t.matched.length-1,o=n.matched.length-1;return s>-1&&s===o&&Gt(t.matched[s],n.matched[o])&&tr(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Gt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function tr(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Gc(e[n],t[n]))return!1;return!0}function Gc(e,t){return Qe(e)?$o(e,t):Qe(t)?$o(t,e):e===t}function $o(e,t){return Qe(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Jc(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),o=s[s.length-1];(o===".."||o===".")&&s.push("");let i=n.length-1,r,l;for(r=0;r<s.length;r++)if(l=s[r],l!==".")if(l==="..")i>1&&i--;else break;return n.slice(0,i).join("/")+"/"+s.slice(r).join("/")}const wt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var _n;(function(e){e.pop="pop",e.push="push"})(_n||(_n={}));var an;(function(e){e.back="back",e.forward="forward",e.unknown=""})(an||(an={}));function Yc(e){if(!e)if(Lt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Qc(e)}const Xc=/^[^#]+#/;function Zc(e,t){return e.replace(Xc,"#")+t}function ea(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const zn=()=>({left:window.scrollX,top:window.scrollY});function ta(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=ea(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Eo(e,t){return(history.state?history.state.position-t:-1)+e}const Ss=new Map;function na(e,t){Ss.set(e,t)}function sa(e){const t=Ss.get(e);return Ss.delete(e),t}let oa=()=>location.protocol+"//"+location.host;function nr(e,t){const{pathname:n,search:s,hash:o}=t,i=e.indexOf("#");if(i>-1){let l=o.includes(e.slice(i))?e.slice(i).length:1,c=o.slice(l);return c[0]!=="/"&&(c="/"+c),Ao(c,"")}return Ao(n,e)+s+o}function ia(e,t,n,s){let o=[],i=[],r=null;const l=({state:p})=>{const m=nr(e,location),x=n.value,$=t.value;let V=0;if(p){if(n.value=m,t.value=p,r&&r===x){r=null;return}V=$?p.position-$.position:0}else s(m);o.forEach(F=>{F(n.value,x,{delta:V,type:_n.pop,direction:V?V>0?an.forward:an.back:an.unknown})})};function c(){r=n.value}function f(p){o.push(p);const m=()=>{const x=o.indexOf(p);x>-1&&o.splice(x,1)};return i.push(m),m}function a(){const{history:p}=window;p.state&&p.replaceState(ee({},p.state,{scroll:zn()}),"")}function u(){for(const p of i)p();i=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",a)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",a,{passive:!0}),{pauseListeners:c,listen:f,destroy:u}}function Ro(e,t,n,s=!1,o=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:o?zn():null}}function ra(e){const{history:t,location:n}=window,s={value:nr(e,n)},o={value:t.state};o.value||i(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(c,f,a){const u=e.indexOf("#"),p=u>-1?(n.host&&document.querySelector("base")?e:e.slice(u))+c:oa()+e+c;try{t[a?"replaceState":"pushState"](f,"",p),o.value=f}catch(m){console.error(m),n[a?"replace":"assign"](p)}}function r(c,f){const a=ee({},t.state,Ro(o.value.back,c,o.value.forward,!0),f,{position:o.value.position});i(c,a,!0),s.value=c}function l(c,f){const a=ee({},o.value,t.state,{forward:c,scroll:zn()});i(a.current,a,!0);const u=ee({},Ro(s.value,c,null),{position:a.position+1},f);i(c,u,!1),s.value=c}return{location:s,state:o,push:l,replace:r}}function la(e){e=Yc(e);const t=ra(e),n=ia(e,t.state,t.location,t.replace);function s(i,r=!0){r||n.pauseListeners(),history.go(i)}const o=ee({location:"",base:e,go:s,createHref:Zc.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function ca(e){return typeof e=="string"||e&&typeof e=="object"}function sr(e){return typeof e=="string"||typeof e=="symbol"}const or=Symbol("");var ko;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(ko||(ko={}));function Jt(e,t){return ee(new Error,{type:e,[or]:!0},t)}function ct(e,t){return e instanceof Error&&or in e&&(t==null||!!(e.type&t))}const Po="[^/]+?",aa={sensitive:!1,strict:!1,start:!0,end:!0},ua=/[.+*?^${}()[\]/\\]/g;function fa(e,t){const n=ee({},aa,t),s=[];let o=n.start?"^":"";const i=[];for(const f of e){const a=f.length?[]:[90];n.strict&&!f.length&&(o+="/");for(let u=0;u<f.length;u++){const p=f[u];let m=40+(n.sensitive?.25:0);if(p.type===0)u||(o+="/"),o+=p.value.replace(ua,"\\$&"),m+=40;else if(p.type===1){const{value:x,repeatable:$,optional:V,regexp:F}=p;i.push({name:x,repeatable:$,optional:V});const M=F||Po;if(M!==Po){m+=10;try{new RegExp(`(${M})`)}catch(I){throw new Error(`Invalid custom RegExp for param "${x}" (${M}): `+I.message)}}let L=$?`((?:${M})(?:/(?:${M}))*)`:`(${M})`;u||(L=V&&f.length<2?`(?:/${L})`:"/"+L),V&&(L+="?"),o+=L,m+=20,V&&(m+=-8),$&&(m+=-20),M===".*"&&(m+=-50)}a.push(m)}s.push(a)}if(n.strict&&n.end){const f=s.length-1;s[f][s[f].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const r=new RegExp(o,n.sensitive?"":"i");function l(f){const a=f.match(r),u={};if(!a)return null;for(let p=1;p<a.length;p++){const m=a[p]||"",x=i[p-1];u[x.name]=m&&x.repeatable?m.split("/"):m}return u}function c(f){let a="",u=!1;for(const p of e){(!u||!a.endsWith("/"))&&(a+="/"),u=!1;for(const m of p)if(m.type===0)a+=m.value;else if(m.type===1){const{value:x,repeatable:$,optional:V}=m,F=x in f?f[x]:"";if(Qe(F)&&!$)throw new Error(`Provided param "${x}" is an array but it is not repeatable (* or + modifiers)`);const M=Qe(F)?F.join("/"):F;if(!M)if(V)p.length<2&&(a.endsWith("/")?a=a.slice(0,-1):u=!0);else throw new Error(`Missing required param "${x}"`);a+=M}}return a||"/"}return{re:r,score:s,keys:i,parse:l,stringify:c}}function da(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function ir(e,t){let n=0;const s=e.score,o=t.score;for(;n<s.length&&n<o.length;){const i=da(s[n],o[n]);if(i)return i;n++}if(Math.abs(o.length-s.length)===1){if(To(s))return 1;if(To(o))return-1}return o.length-s.length}function To(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ha={type:0,value:""},pa=/[a-zA-Z0-9_]/;function ma(e){if(!e)return[[]];if(e==="/")return[[ha]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${f}": ${m}`)}let n=0,s=n;const o=[];let i;function r(){i&&o.push(i),i=[]}let l=0,c,f="",a="";function u(){f&&(n===0?i.push({type:0,value:f}):n===1||n===2||n===3?(i.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${f}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:f,regexp:a,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),f="")}function p(){f+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(f&&u(),r()):c===":"?(u(),n=1):p();break;case 4:p(),n=s;break;case 1:c==="("?n=2:pa.test(c)?p():(u(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?a[a.length-1]=="\\"?a=a.slice(0,-1)+c:n=3:a+=c;break;case 3:u(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,a="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${f}"`),u(),r(),o}function ga(e,t,n){const s=fa(ma(e.path),n),o=ee(s,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function va(e,t){const n=[],s=new Map;t=Mo({strict:!1,end:!0,sensitive:!1},t);function o(u){return s.get(u)}function i(u,p,m){const x=!m,$=Oo(u);$.aliasOf=m&&m.record;const V=Mo(t,u),F=[$];if("alias"in u){const I=typeof u.alias=="string"?[u.alias]:u.alias;for(const X of I)F.push(Oo(ee({},$,{components:m?m.record.components:$.components,path:X,aliasOf:m?m.record:$})))}let M,L;for(const I of F){const{path:X}=I;if(p&&X[0]!=="/"){const ve=p.record.path,ie=ve[ve.length-1]==="/"?"":"/";I.path=p.record.path+(X&&ie+X)}if(M=ga(I,p,V),m?m.alias.push(M):(L=L||M,L!==M&&L.alias.push(M),x&&u.name&&!Io(M)&&r(u.name)),rr(M)&&c(M),$.children){const ve=$.children;for(let ie=0;ie<ve.length;ie++)i(ve[ie],M,m&&m.children[ie])}m=m||M}return L?()=>{r(L)}:cn}function r(u){if(sr(u)){const p=s.get(u);p&&(s.delete(u),n.splice(n.indexOf(p),1),p.children.forEach(r),p.alias.forEach(r))}else{const p=n.indexOf(u);p>-1&&(n.splice(p,1),u.record.name&&s.delete(u.record.name),u.children.forEach(r),u.alias.forEach(r))}}function l(){return n}function c(u){const p=ba(u,n);n.splice(p,0,u),u.record.name&&!Io(u)&&s.set(u.record.name,u)}function f(u,p){let m,x={},$,V;if("name"in u&&u.name){if(m=s.get(u.name),!m)throw Jt(1,{location:u});V=m.record.name,x=ee(No(p.params,m.keys.filter(L=>!L.optional).concat(m.parent?m.parent.keys.filter(L=>L.optional):[]).map(L=>L.name)),u.params&&No(u.params,m.keys.map(L=>L.name))),$=m.stringify(x)}else if(u.path!=null)$=u.path,m=n.find(L=>L.re.test($)),m&&(x=m.parse($),V=m.record.name);else{if(m=p.name?s.get(p.name):n.find(L=>L.re.test(p.path)),!m)throw Jt(1,{location:u,currentLocation:p});V=m.record.name,x=ee({},p.params,u.params),$=m.stringify(x)}const F=[];let M=m;for(;M;)F.unshift(M.record),M=M.parent;return{name:V,path:$,params:x,matched:F,meta:ya(F)}}e.forEach(u=>i(u));function a(){n.length=0,s.clear()}return{addRoute:i,resolve:f,removeRoute:r,clearRoutes:a,getRoutes:l,getRecordMatcher:o}}function No(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Oo(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:_a(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function _a(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Io(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function ya(e){return e.reduce((t,n)=>ee(t,n.meta),{})}function Mo(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function ba(e,t){let n=0,s=t.length;for(;n!==s;){const i=n+s>>1;ir(e,t[i])<0?s=i:n=i+1}const o=wa(e);return o&&(s=t.lastIndexOf(o,s-1)),s}function wa(e){let t=e;for(;t=t.parent;)if(rr(t)&&ir(e,t)===0)return t}function rr({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function xa(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<s.length;++o){const i=s[o].replace(Yi," "),r=i.indexOf("="),l=vn(r<0?i:i.slice(0,r)),c=r<0?null:vn(i.slice(r+1));if(l in t){let f=t[l];Qe(f)||(f=t[l]=[f]),f.push(c)}else t[l]=c}return t}function Do(e){let t="";for(let n in e){const s=e[n];if(n=Vc(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(Qe(s)?s.map(i=>i&&Cs(i)):[s&&Cs(s)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+n,i!=null&&(t+="="+i))})}return t}function Ca(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=Qe(s)?s.map(o=>o==null?null:""+o):s==null?s:""+s)}return t}const Sa=Symbol(""),Fo=Symbol(""),Gn=Symbol(""),lr=Symbol(""),As=Symbol("");function en(){let e=[];function t(s){return e.push(s),()=>{const o=e.indexOf(s);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function St(e,t,n,s,o,i=r=>r()){const r=s&&(s.enterCallbacks[o]=s.enterCallbacks[o]||[]);return()=>new Promise((l,c)=>{const f=p=>{p===!1?c(Jt(4,{from:n,to:t})):p instanceof Error?c(p):ca(p)?c(Jt(2,{from:t,to:p})):(r&&s.enterCallbacks[o]===r&&typeof p=="function"&&r.push(p),l())},a=i(()=>e.call(s&&s.instances[o],t,n,f));let u=Promise.resolve(a);e.length<3&&(u=u.then(f)),u.catch(p=>c(p))})}function us(e,t,n,s,o=i=>i()){const i=[];for(const r of e)for(const l in r.components){let c=r.components[l];if(!(t!=="beforeRouteEnter"&&!r.instances[l]))if(Gi(c)){const a=(c.__vccOpts||c)[t];a&&i.push(St(a,n,s,r,l,o))}else{let f=c();i.push(()=>f.then(a=>{if(!a)throw new Error(`Couldn't resolve component "${l}" at "${r.path}"`);const u=Tc(a)?a.default:a;r.mods[l]=a,r.components[l]=u;const m=(u.__vccOpts||u)[t];return m&&St(m,n,s,r,l,o)()}))}}return i}function jo(e){const t=Ue(Gn),n=Ue(lr),s=re(()=>{const c=A(e.to);return t.resolve(c)}),o=re(()=>{const{matched:c}=s.value,{length:f}=c,a=c[f-1],u=n.matched;if(!a||!u.length)return-1;const p=u.findIndex(Gt.bind(null,a));if(p>-1)return p;const m=Lo(c[f-2]);return f>1&&Lo(a)===m&&u[u.length-1].path!==m?u.findIndex(Gt.bind(null,c[f-2])):p}),i=re(()=>o.value>-1&&ka(n.params,s.value.params)),r=re(()=>o.value>-1&&o.value===n.matched.length-1&&tr(n.params,s.value.params));function l(c={}){if(Ra(c)){const f=t[A(e.replace)?"replace":"push"](A(e.to)).catch(cn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>f),f}return Promise.resolve()}return{route:s,href:re(()=>s.value.href),isActive:i,isExactActive:r,navigate:l}}function Aa(e){return e.length===1?e[0]:e}const $a=yi({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:jo,setup(e,{slots:t}){const n=Ot(jo(e)),{options:s}=Ue(Gn),o=re(()=>({[Bo(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[Bo(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const i=t.default&&Aa(t.default(n));return e.custom?i:Wi("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},i)}}}),Ea=$a;function Ra(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function ka(e,t){for(const n in t){const s=t[n],o=e[n];if(typeof s=="string"){if(s!==o)return!1}else if(!Qe(o)||o.length!==s.length||s.some((i,r)=>i!==o[r]))return!1}return!0}function Lo(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Bo=(e,t,n)=>e??t??n,Pa=yi({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=Ue(As),o=re(()=>e.route||s.value),i=Ue(Fo,0),r=re(()=>{let f=A(i);const{matched:a}=o.value;let u;for(;(u=a[f])&&!u.components;)f++;return f}),l=re(()=>o.value.matched[r.value]);Wt(Fo,re(()=>r.value+1)),Wt(Sa,l),Wt(As,o);const c=Z();return nt(()=>[c.value,l.value,e.name],([f,a,u],[p,m,x])=>{a&&(a.instances[u]=f,m&&m!==a&&f&&f===p&&(a.leaveGuards.size||(a.leaveGuards=m.leaveGuards),a.updateGuards.size||(a.updateGuards=m.updateGuards))),f&&a&&(!m||!Gt(a,m)||!p)&&(a.enterCallbacks[u]||[]).forEach($=>$(f))},{flush:"post"}),()=>{const f=o.value,a=e.name,u=l.value,p=u&&u.components[a];if(!p)return Ho(n.default,{Component:p,route:f});const m=u.props[a],x=m?m===!0?f.params:typeof m=="function"?m(f):m:null,V=Wi(p,ee({},x,t,{onVnodeUnmounted:F=>{F.component.isUnmounted&&(u.instances[a]=null)},ref:c}));return Ho(n.default,{Component:V,route:f})||V}}});function Ho(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Ta=Pa;function Na(e){const t=va(e.routes,e),n=e.parseQuery||xa,s=e.stringifyQuery||Do,o=e.history,i=en(),r=en(),l=en(),c=Ur(wt);let f=wt;Lt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const a=cs.bind(null,y=>""+y),u=cs.bind(null,Kc),p=cs.bind(null,vn);function m(y,O){let P,D;return sr(y)?(P=t.getRecordMatcher(y),D=O):D=y,t.addRoute(D,P)}function x(y){const O=t.getRecordMatcher(y);O&&t.removeRoute(O)}function $(){return t.getRoutes().map(y=>y.record)}function V(y){return!!t.getRecordMatcher(y)}function F(y,O){if(O=ee({},O||c.value),typeof y=="string"){const g=as(n,y,O.path),_=t.resolve({path:g.path},O),w=o.createHref(g.fullPath);return ee(g,_,{params:p(_.params),hash:vn(g.hash),redirectedFrom:void 0,href:w})}let P;if(y.path!=null)P=ee({},y,{path:as(n,y.path,O.path).path});else{const g=ee({},y.params);for(const _ in g)g[_]==null&&delete g[_];P=ee({},y,{params:u(g)}),O.params=u(O.params)}const D=t.resolve(P,O),ce=y.hash||"";D.params=a(p(D.params));const d=Wc(s,ee({},y,{hash:Hc(ce),path:D.path})),h=o.createHref(d);return ee({fullPath:d,hash:ce,query:s===Do?Ca(y.query):y.query||{}},D,{redirectedFrom:void 0,href:h})}function M(y){return typeof y=="string"?as(n,y,c.value.path):ee({},y)}function L(y,O){if(f!==y)return Jt(8,{from:O,to:y})}function I(y){return ie(y)}function X(y){return I(ee(M(y),{replace:!0}))}function ve(y){const O=y.matched[y.matched.length-1];if(O&&O.redirect){const{redirect:P}=O;let D=typeof P=="function"?P(y):P;return typeof D=="string"&&(D=D.includes("?")||D.includes("#")?D=M(D):{path:D},D.params={}),ee({query:y.query,hash:y.hash,params:D.path!=null?{}:y.params},D)}}function ie(y,O){const P=f=F(y),D=c.value,ce=y.state,d=y.force,h=y.replace===!0,g=ve(P);if(g)return ie(ee(M(g),{state:typeof g=="object"?ee({},ce,g.state):ce,force:d,replace:h}),O||P);const _=P;_.redirectedFrom=O;let w;return!d&&zc(s,D,P)&&(w=Jt(16,{to:_,from:D}),We(D,D,!0,!1)),(w?Promise.resolve(w):we(_,D)).catch(b=>ct(b)?ct(b,2)?b:bt(b):W(b,_,D)).then(b=>{if(b){if(ct(b,2))return ie(ee({replace:h},M(b.to),{state:typeof b.to=="object"?ee({},ce,b.to.state):ce,force:d}),O||_)}else b=xe(_,D,!0,h,ce);return Ce(_,D,b),b})}function z(y,O){const P=L(y,O);return P?Promise.reject(P):Promise.resolve()}function Y(y){const O=Mt.values().next().value;return O&&typeof O.runWithContext=="function"?O.runWithContext(y):y()}function we(y,O){let P;const[D,ce,d]=Oa(y,O);P=us(D.reverse(),"beforeRouteLeave",y,O);for(const g of D)g.leaveGuards.forEach(_=>{P.push(St(_,y,O))});const h=z.bind(null,y,O);return P.push(h),He(P).then(()=>{P=[];for(const g of i.list())P.push(St(g,y,O));return P.push(h),He(P)}).then(()=>{P=us(ce,"beforeRouteUpdate",y,O);for(const g of ce)g.updateGuards.forEach(_=>{P.push(St(_,y,O))});return P.push(h),He(P)}).then(()=>{P=[];for(const g of d)if(g.beforeEnter)if(Qe(g.beforeEnter))for(const _ of g.beforeEnter)P.push(St(_,y,O));else P.push(St(g.beforeEnter,y,O));return P.push(h),He(P)}).then(()=>(y.matched.forEach(g=>g.enterCallbacks={}),P=us(d,"beforeRouteEnter",y,O,Y),P.push(h),He(P))).then(()=>{P=[];for(const g of r.list())P.push(St(g,y,O));return P.push(h),He(P)}).catch(g=>ct(g,8)?g:Promise.reject(g))}function Ce(y,O,P){l.list().forEach(D=>Y(()=>D(y,O,P)))}function xe(y,O,P,D,ce){const d=L(y,O);if(d)return d;const h=O===wt,g=Lt?history.state:{};P&&(D||h?o.replace(y.fullPath,ee({scroll:h&&g&&g.scroll},ce)):o.push(y.fullPath,ce)),c.value=y,We(y,O,P,h),bt()}let _e;function Rt(){_e||(_e=o.listen((y,O,P)=>{if(!wn.listening)return;const D=F(y),ce=ve(D);if(ce){ie(ee(ce,{replace:!0,force:!0}),D).catch(cn);return}f=D;const d=c.value;Lt&&na(Eo(d.fullPath,P.delta),zn()),we(D,d).catch(h=>ct(h,12)?h:ct(h,2)?(ie(ee(M(h.to),{force:!0}),D).then(g=>{ct(g,20)&&!P.delta&&P.type===_n.pop&&o.go(-1,!1)}).catch(cn),Promise.reject()):(P.delta&&o.go(-P.delta,!1),W(h,D,d))).then(h=>{h=h||xe(D,d,!1),h&&(P.delta&&!ct(h,8)?o.go(-P.delta,!1):P.type===_n.pop&&ct(h,20)&&o.go(-1,!1)),Ce(D,d,h)}).catch(cn)}))}let yt=en(),pe=en(),G;function W(y,O,P){bt(y);const D=pe.list();return D.length?D.forEach(ce=>ce(y,O,P)):console.error(y),Promise.reject(y)}function rt(){return G&&c.value!==wt?Promise.resolve():new Promise((y,O)=>{yt.add([y,O])})}function bt(y){return G||(G=!y,Rt(),yt.list().forEach(([O,P])=>y?P(y):O()),yt.reset()),y}function We(y,O,P,D){const{scrollBehavior:ce}=e;if(!Lt||!ce)return Promise.resolve();const d=!P&&sa(Eo(y.fullPath,0))||(D||!P)&&history.state&&history.state.scroll||null;return Fs().then(()=>ce(y,O,d)).then(h=>h&&ta(h)).catch(h=>W(h,y,O))}const Ne=y=>o.go(y);let It;const Mt=new Set,wn={currentRoute:c,listening:!0,addRoute:m,removeRoute:x,clearRoutes:t.clearRoutes,hasRoute:V,getRoutes:$,resolve:F,options:e,push:I,replace:X,go:Ne,back:()=>Ne(-1),forward:()=>Ne(1),beforeEach:i.add,beforeResolve:r.add,afterEach:l.add,onError:pe.add,isReady:rt,install(y){const O=this;y.component("RouterLink",Ea),y.component("RouterView",Ta),y.config.globalProperties.$router=O,Object.defineProperty(y.config.globalProperties,"$route",{enumerable:!0,get:()=>A(c)}),Lt&&!It&&c.value===wt&&(It=!0,I(o.location).catch(ce=>{}));const P={};for(const ce in wt)Object.defineProperty(P,ce,{get:()=>c.value[ce],enumerable:!0});y.provide(Gn,O),y.provide(lr,ui(P)),y.provide(As,c);const D=y.unmount;Mt.add(y),y.unmount=function(){Mt.delete(y),Mt.size<1&&(f=wt,_e&&_e(),_e=null,c.value=wt,It=!1,G=!1),D()}}};function He(y){return y.reduce((O,P)=>O.then(()=>Y(P)),Promise.resolve())}return wn}function Oa(e,t){const n=[],s=[],o=[],i=Math.max(t.matched.length,e.matched.length);for(let r=0;r<i;r++){const l=t.matched[r];l&&(e.matched.find(f=>Gt(f,l))?s.push(l):n.push(l));const c=e.matched[r];c&&(t.matched.find(f=>Gt(f,c))||o.push(c))}return[n,s,o]}function Ks(){return Ue(Gn)}function cr(){const e=Z([{id:1,message:"Mr A updated a document (Click to view)",classId:1,documentId:1,timestamp:new Date(Date.now()-18e5),read:!1},{id:2,message:"Mr B updated a document (Click to view)",classId:2,documentId:2,timestamp:new Date(Date.now()-72e5),read:!1}]),t=re(()=>e.value.filter(l=>!l.read).length),n=re(()=>[...e.value].sort((l,c)=>c.timestamp-l.timestamp));return{notifications:e,unreadCount:t,sortedNotifications:n,markAsRead:l=>{const c=e.value.find(f=>f.id===l);c&&(c.read=!0)},markAllAsRead:()=>{e.value.forEach(l=>l.read=!0)},addNotification:(l,c,f)=>{const a={id:Date.now(),message:l,classId:c,documentId:f,timestamp:new Date,read:!1};return e.value.unshift(a),a},removeNotification:l=>{const c=e.value.findIndex(f=>f.id===l);c>-1&&e.value.splice(c,1)}}}const it=(e,t)=>{const n=e.__vccOpts||e;for(const[s,o]of t)n[s]=o;return n},Ia={key:0,class:"card-header d-flex justify-content-between align-items-center bg-violet"},Ma={class:"card-title h5 mb-0 text-white"},Da={class:"card-body p-3"},Fa={key:1,class:"card-footer"},ja={__name:"BaseCard",props:{title:{type:String,default:""},variant:{type:String,default:"default",validator:e=>["default","primary","secondary","success","danger","warning","info","light","dark"].includes(e)},clickable:{type:Boolean,default:!1},selected:{type:Boolean,default:!1}},emits:["click"],setup(e,{emit:t}){const n=e,s=t,o=re(()=>{const r=["card","h-100"];return n.clickable&&r.push("card-clickable"),n.selected&&r.push("border-primary","bg-primary","bg-opacity-10"),r.join(" ")}),i=r=>{n.clickable&&s("click",r)};return(r,l)=>(C(),N("div",{class:Me(o.value),onClick:i},[r.$slots.header||e.title?(C(),N("div",Ia,[Bt(r.$slots,"header",{},()=>[v("h2",Ma,Q(e.title),1)]),Bt(r.$slots,"header-actions",{},void 0)])):le("",!0),v("div",Da,[Bt(r.$slots,"default",{},void 0)]),r.$slots.footer?(C(),N("div",Fa,[Bt(r.$slots,"footer",{},void 0)])):le("",!0)],2))}},_t=it(ja,[["__scopeId","data-v-0b85be82"]]),La=["disabled"],Ba={__name:"BaseButton",props:{variant:{type:String,default:"primary",validator:e=>["primary","secondary","success","danger","warning","info","light","dark","outline-primary","outline-secondary","outline-success","outline-danger","outline-warning","outline-info","outline-light","outline-dark","purple"].includes(e)},size:{type:String,default:"md",validator:e=>["sm","md","lg"].includes(e)},disabled:{type:Boolean,default:!1},icon:{type:String,default:"",validator:e=>!e||e.startsWith("bi-")}},emits:["click"],setup(e){const t=e,n=re(()=>{const s=["btn"];return t.variant==="purple"?s.push("btn-purple"):s.push(`btn-${t.variant}`),t.size!=="md"&&s.push(`btn-${t.size}`),s.join(" ")});return(s,o)=>(C(),N("button",{class:Me(n.value),disabled:e.disabled,onClick:o[0]||(o[0]=i=>s.$emit("click",i))},[e.icon?(C(),N("i",{key:0,class:Me([`bi ${e.icon}`,{"me-2":s.$slots.default}]),"aria-hidden":"true"},null,2)):le("",!0),Bt(s.$slots,"default",{},void 0)],10,La))}},ge=it(Ba,[["__scopeId","data-v-ac23806e"]]);function Jn(){const e=Ue("selectedClass"),t=Ue("selectClass"),n=Z([]),s=Z([]),o=Z([]),i=Ot({subjects:["math","english","science"],classAverage:[8.2,7.5,8.8],reachedNorm:["yes","no","yes"],passingPerc:["85%","72%","91%"]}),r=re(()=>e.value?s.value.filter(x=>x.classId===e.value.id):[]),l=re(()=>e.value?o.value.filter(x=>x.classId===e.value.id):[]),c=re(()=>e.value?i:{subjects:[],classAverage:[],reachedNorm:[],passingPerc:[]}),f=x=>{const $={id:Date.now(),name:x.name,teacher:`${x.teacherFirstName} ${x.teacherLastName}`};return n.value.push($),$},a=x=>{if(!e.value)return null;const $={id:Date.now(),name:x.trim(),classId:e.value.id};return s.value.push($),$},u=x=>{if(!e.value)return null;const $={id:Date.now(),title:x.title,lastEdited:new Date().toLocaleDateString("en-GB"),editor:x.editor,classId:e.value.id};return o.value.push($),$},p=()=>{n.value=[{id:1,name:"Class 5",teacher:"Mr. Smith"},{id:2,name:"Class 6",teacher:"Ms. Johnson"}],s.value=[{id:1,name:"Jimmy Studentname",classId:1},{id:2,name:"Ash Ketchum",classId:1},{id:3,name:"Pikachu",classId:1},{id:4,name:"Brock Harrison",classId:1},{id:5,name:"Girl Name",classId:1},{id:6,name:"John Doe",classId:2},{id:7,name:"Jane Smith",classId:2}],o.value=[{id:1,title:"May 2025",lastEdited:"15/01/2025",editor:"Mr. Smith",classId:1},{id:2,title:"April 2025",lastEdited:"10/01/2025",editor:"Mr. Smith",classId:1},{id:3,title:"June 2025",lastEdited:"12/01/2025",editor:"Ms. Johnson",classId:2}]};return n.value.length===0&&p(),{classesData:n,studentsData:s,documentsData:o,statisticsData:i,selectedClass:e,filteredStudents:r,filteredDocuments:l,filteredStatistics:c,addClass:f,addStudent:a,addDocument:u,handleClassSelect:x=>{t(x)}}}const Ha={class:"mb-3"},Va={class:"row"},Ua={class:"col-md-6 mb-3"},Ka={class:"col-md-6 mb-3"},qa={class:"d-flex gap-2"},Qa={__name:"ClassForm",emits:["submit","cancel"],setup(e,{emit:t}){const n=t,s=Ot({name:"",teacherFirstName:"",teacherLastName:""}),o=()=>{s.name&&s.teacherFirstName&&s.teacherLastName&&(n("submit",{...s}),s.name="",s.teacherFirstName="",s.teacherLastName="")};return(i,r)=>(C(),me(_t,{title:"Add New Class"},{default:se(()=>[v("form",{onSubmit:mt(o,["prevent"])},[v("div",Ha,[tt(v("input",{"onUpdate:modelValue":r[0]||(r[0]=l=>s.name=l),placeholder:"Class name (e.g., class 5)",class:"form-control",required:""},null,512),[[st,s.name]])]),v("div",Va,[v("div",Ua,[tt(v("input",{"onUpdate:modelValue":r[1]||(r[1]=l=>s.teacherFirstName=l),placeholder:"Teacher first name",class:"form-control",required:""},null,512),[[st,s.teacherFirstName]])]),v("div",Ka,[tt(v("input",{"onUpdate:modelValue":r[2]||(r[2]=l=>s.teacherLastName=l),placeholder:"Teacher last name",class:"form-control",required:""},null,512),[[st,s.teacherLastName]])])]),v("div",qa,[K(ge,{type:"submit",variant:"purple",icon:"bi-plus-lg"},{default:se(()=>r[4]||(r[4]=[ye(" Add Class ")])),_:1,__:[4]}),K(ge,{type:"button",variant:"secondary",icon:"bi-x-lg",onClick:r[3]||(r[3]=l=>i.$emit("cancel"))},{default:se(()=>r[5]||(r[5]=[ye(" Cancel ")])),_:1,__:[5]})])],32)]),_:1}))}},Wa={class:"classes-content"},za={key:1,class:"row g-2"},Ga={class:"card-title mb-1"},Ja={class:"card-text small text-muted mb-0"},Ya={key:2,class:"text-center text-muted py-4"},Xa={__name:"ClassesContent",props:{showAddForm:{type:Boolean,default:!1}},emits:["form-visibility-change"],setup(e,{emit:t}){const n=e,s=t,{classesData:o,selectedClass:i,addClass:r,handleClassSelect:l}=Jn(),c=Z(n.showAddForm);nt(()=>n.showAddForm,a=>{c.value=a}),nt(c,a=>{s("form-visibility-change",a)});const f=a=>{r(a),c.value=!1};return(a,u)=>(C(),N("div",Wa,[c.value?(C(),me(Qa,{key:0,onSubmit:f,onCancel:u[0]||(u[0]=p=>c.value=!1),class:"mb-3"})):le("",!0),A(o).length>0?(C(),N("div",za,[(C(!0),N(de,null,Le(A(o),p=>(C(),N("div",{key:p.id,class:"col-md-6"},[K(_t,{clickable:!0,selected:A(i)&&A(i).id===p.id,onClick:m=>A(l)(p)},{default:se(()=>[v("h6",Ga,Q(p.name),1),v("p",Ja,Q(p.teacher),1)]),_:2},1032,["selected","onClick"])]))),128))])):le("",!0),A(o).length===0&&!c.value?(C(),N("div",Ya,u[1]||(u[1]=[v("p",{class:"mb-0"},"No classes added yet.",-1)]))):le("",!0)]))}},Za={class:"mb-3"},eu={class:"d-flex gap-2"},tu={__name:"StudentForm",emits:["submit","cancel"],setup(e,{emit:t}){const n=t,s=Z(""),o=()=>{s.value.trim()&&(n("submit",s.value.trim()),s.value="")};return(i,r)=>(C(),me(_t,{title:"Add New Student"},{default:se(()=>[v("form",{onSubmit:mt(o,["prevent"])},[v("div",Za,[tt(v("input",{"onUpdate:modelValue":r[0]||(r[0]=l=>s.value=l),placeholder:"Student name (e.g., John Doe)",class:"form-control",required:""},null,512),[[st,s.value]])]),v("div",eu,[K(ge,{type:"submit",variant:"purple",icon:"bi-person-plus"},{default:se(()=>r[2]||(r[2]=[ye(" Add Student ")])),_:1,__:[2]}),K(ge,{type:"button",variant:"secondary",icon:"bi-x-lg",onClick:r[1]||(r[1]=l=>i.$emit("cancel"))},{default:se(()=>r[3]||(r[3]=[ye(" Cancel ")])),_:1,__:[3]})])],32)]),_:1}))}},nu={class:"students-content"},su={key:1,class:"row g-2"},ou={class:"text-center"},iu={class:"small"},ru={key:2,class:"text-center text-muted py-4"},lu={key:3,class:"text-center text-muted py-4"},cu={class:"mb-0"},au={__name:"StudentsContent",props:{showAddForm:{type:Boolean,default:!1}},emits:["form-visibility-change"],setup(e,{emit:t}){const n=e,s=t,{filteredStudents:o,selectedClass:i,addStudent:r}=Jn(),l=Z(n.showAddForm);nt(()=>n.showAddForm,f=>{l.value=f}),nt(l,f=>{s("form-visibility-change",f)});const c=f=>{if(!i.value){alert("Please select a class first before adding students.");return}r(f),l.value=!1};return(f,a)=>(C(),N("div",nu,[l.value?(C(),me(tu,{key:0,onSubmit:c,onCancel:a[0]||(a[0]=u=>l.value=!1),class:"mb-3"})):le("",!0),A(o).length>0?(C(),N("div",su,[(C(!0),N(de,null,Le(A(o),u=>(C(),N("div",{key:u.id,class:"col-md-6"},[K(_t,null,{default:se(()=>[v("div",ou,[v("span",iu,Q(u.name),1)])]),_:2},1024)]))),128))])):le("",!0),!A(i)&&!l.value?(C(),N("div",ru,a[1]||(a[1]=[v("p",{class:"mb-0"},"Please select a class first to view and add students.",-1)]))):A(i)&&A(o).length===0&&!l.value?(C(),N("div",lu,[v("p",cu,"No students in "+Q(A(i).name)+' yet. Click "+Add Student" to get started.',1)])):le("",!0)]))}},uu={class:"mb-3"},fu={class:"mb-3"},du={class:"d-flex gap-2"},hu={__name:"DocumentForm",emits:["submit","cancel"],setup(e,{emit:t}){const n=t,s=Ot({title:"",editor:""}),o=()=>{s.title&&s.editor&&(n("submit",{...s}),s.title="",s.editor="")};return(i,r)=>(C(),me(_t,{title:"Add New Document"},{default:se(()=>[v("form",{onSubmit:mt(o,["prevent"])},[v("div",uu,[tt(v("input",{"onUpdate:modelValue":r[0]||(r[0]=l=>s.title=l),placeholder:"Document title (e.g., May 2025)",class:"form-control",required:""},null,512),[[st,s.title]])]),v("div",fu,[tt(v("input",{"onUpdate:modelValue":r[1]||(r[1]=l=>s.editor=l),placeholder:"Editor name (e.g., Mr. Smith)",class:"form-control",required:""},null,512),[[st,s.editor]])]),v("div",du,[K(ge,{type:"submit",variant:"purple",icon:"bi-file-plus"},{default:se(()=>r[3]||(r[3]=[ye(" Add Document ")])),_:1,__:[3]}),K(ge,{type:"button",variant:"secondary",icon:"bi-x-lg",onClick:r[2]||(r[2]=l=>i.$emit("cancel"))},{default:se(()=>r[4]||(r[4]=[ye(" Cancel ")])),_:1,__:[4]})])],32)]),_:1}))}},pu={class:"documents-content"},mu={key:1,class:"list-group"},gu=["onClick"],vu={class:"d-flex w-100 justify-content-between"},_u={class:"mb-1"},yu={class:"text-muted"},bu={key:2,class:"text-center text-muted py-4"},wu={key:3,class:"text-center text-muted py-4"},xu={class:"mb-0"},Cu={__name:"DocumentsContent",props:{showAddForm:{type:Boolean,default:!1}},emits:["form-visibility-change"],setup(e,{emit:t}){const n=Ks(),s=e,o=t,{filteredDocuments:i,selectedClass:r,addDocument:l}=Jn(),c=Z(s.showAddForm);nt(()=>s.showAddForm,u=>{c.value=u}),nt(c,u=>{o("form-visibility-change",u)});const f=u=>{if(!r.value){alert("Please select a class first before adding documents.");return}l(u),c.value=!1},a=u=>{if(!r.value){alert("Please select a class first.");return}n.push(`/document/${r.value.id}/${u.id}`)};return(u,p)=>(C(),N("div",pu,[c.value?(C(),me(hu,{key:0,onSubmit:f,onCancel:p[0]||(p[0]=m=>c.value=!1),class:"mb-3"})):le("",!0),A(i).length>0?(C(),N("div",mu,[(C(!0),N(de,null,Le(A(i),m=>(C(),N("div",{key:m.id,class:"list-group-item list-group-item-action cursor-pointer",onClick:x=>a(m)},[v("div",vu,[v("h6",_u,Q(m.title),1),v("small",null,Q(m.lastEdited),1)]),v("small",yu,"Edited by "+Q(m.editor),1)],8,gu))),128))])):le("",!0),!A(r)&&!c.value?(C(),N("div",bu,p[1]||(p[1]=[v("p",{class:"mb-0"},"Please select a class first to view and add documents.",-1)]))):A(r)&&A(i).length===0&&!c.value?(C(),N("div",wu,[v("p",xu,"No documents for "+Q(A(r).name)+' yet. Click "+New Document" to get started.',1)])):le("",!0)]))}},Su={class:"statistics-content"},Au={key:0},$u={class:"table table-striped table-sm"},Eu={class:"table-dark"},Ru={key:1,class:"text-center text-muted py-4"},ku={key:2,class:"text-center text-muted py-4"},Pu={class:"mb-0"},Tu={__name:"StatisticsContent",setup(e){const{filteredStatistics:t,selectedClass:n}=Jn();return(s,o)=>(C(),N("div",Su,[A(n)&&A(t).subjects.length>0?(C(),N("div",Au,[v("table",$u,[v("thead",Eu,[v("tr",null,[o[0]||(o[0]=v("th",{scope:"col"},null,-1)),(C(!0),N(de,null,Le(A(t).subjects,i=>(C(),N("th",{scope:"col",key:i},Q(i),1))),128))])]),v("tbody",null,[v("tr",null,[o[1]||(o[1]=v("th",{scope:"row",class:"text-muted"},"Class Average",-1)),(C(!0),N(de,null,Le(A(t).classAverage,(i,r)=>(C(),N("td",{key:r},Q(i),1))),128))]),v("tr",null,[o[2]||(o[2]=v("th",{scope:"row",class:"text-muted"},"Reached Norm",-1)),(C(!0),N(de,null,Le(A(t).reachedNorm,(i,r)=>(C(),N("td",{key:r},[v("span",{class:Me(i==="yes"?"badge bg-success":"badge bg-danger")},Q(i),3)]))),128))]),v("tr",null,[o[3]||(o[3]=v("th",{scope:"row",class:"text-muted"},"Passing %",-1)),(C(!0),N(de,null,Le(A(t).passingPerc,(i,r)=>(C(),N("td",{key:r},Q(i),1))),128))])])])])):le("",!0),A(n)?A(n)&&A(t).subjects.length===0?(C(),N("div",ku,[v("p",Pu,"No statistics data available for "+Q(A(n).name)+" yet.",1)])):le("",!0):(C(),N("div",Ru,o[4]||(o[4]=[v("p",{class:"mb-0"},"Please select a class first to view statistics.",-1)])))]))}},Nu={key:4,class:"default-content"},Ou={__name:"QuadrantCard",props:{title:{type:String,default:"Quadrant Title"},addButtonText:{type:String,default:"Add Item"},type:{type:String,default:"default",validator:e=>["classes","students","documents","statistics","default"].includes(e)}},setup(e){const t=Z(!1),n=()=>{t.value=!0},s=o=>{t.value=o};return(o,i)=>(C(),me(_t,{title:e.title},{"header-actions":se(()=>[e.addButtonText&&e.type!=="statistics"?(C(),me(ge,{key:0,onClick:n,variant:"purple",size:"sm",icon:"bi-plus-lg"},{default:se(()=>[ye(Q(e.addButtonText),1)]),_:1})):le("",!0)]),default:se(()=>[e.type==="classes"?(C(),me(Xa,{key:0,"show-add-form":t.value,onFormVisibilityChange:s},null,8,["show-add-form"])):e.type==="students"?(C(),me(au,{key:1,"show-add-form":t.value,onFormVisibilityChange:s},null,8,["show-add-form"])):e.type==="documents"?(C(),me(Cu,{key:2,"show-add-form":t.value,onFormVisibilityChange:s},null,8,["show-add-form"])):e.type==="statistics"?(C(),me(Tu,{key:3})):(C(),N("div",Nu,[Bt(o.$slots,"default",{},()=>[i[0]||(i[0]=v("div",{class:"placeholder-content"},[v("p",{class:"placeholder-text"},"Add your content here")],-1))],!0)]))]),_:3},8,["title"]))}},An=it(Ou,[["__scopeId","data-v-bcdc44ce"]]),Iu={class:"notification-header"},Mu={class:"d-flex gap-2"},Du={class:"notification-body"},Fu={key:0,class:"text-center text-muted py-4"},ju={key:1,class:"notification-list"},Lu=["onClick"],Bu={class:"notification-content"},Hu={class:"notification-message mb-1"},Vu={class:"text-muted"},Uu={class:"notification-actions"},Ku={__name:"NotificationDialog",emits:["close","document-clicked"],setup(e,{emit:t}){const n=t,{sortedNotifications:s,unreadCount:o,markAsRead:i,markAllAsRead:r,removeNotification:l}=cr(),c=()=>{n("close")},f=u=>{i(u.id),n("document-clicked",u.classId,u.documentId)},a=u=>{const m=new Date-u,x=Math.floor(m/(1e3*60)),$=Math.floor(m/(1e3*60*60)),V=Math.floor(m/(1e3*60*60*24));return x<1?"Just now":x<60?`${x}m ago`:$<24?`${$}h ago`:`${V}d ago`};return(u,p)=>(C(),N("div",{class:"notification-overlay",onClick:c},[v("div",{class:"notification-dialog",onClick:p[1]||(p[1]=mt(()=>{},["stop"]))},[v("div",Iu,[p[3]||(p[3]=v("h5",{class:"mb-0"},"Notifications",-1)),v("div",Mu,[A(o)>0?(C(),me(ge,{key:0,variant:"outline-secondary",size:"sm",onClick:A(r)},{default:se(()=>p[2]||(p[2]=[ye(" Mark all read ")])),_:1,__:[2]},8,["onClick"])):le("",!0),K(ge,{variant:"outline-secondary",size:"sm",icon:"bi-x-lg",onClick:p[0]||(p[0]=m=>u.$emit("close"))})])]),v("div",Du,[A(s).length===0?(C(),N("div",Fu,p[4]||(p[4]=[v("i",{class:"bi bi-bell-slash fs-1 mb-2"},null,-1),v("p",{class:"mb-0"},"No notifications",-1)]))):(C(),N("div",ju,[(C(!0),N(de,null,Le(A(s),m=>(C(),N("div",{key:m.id,class:Me(["notification-item",{unread:!m.read}]),onClick:x=>f(m)},[v("div",Bu,[v("p",Hu,Q(m.message),1),v("small",Vu,Q(a(m.timestamp)),1)]),v("div",Uu,[K(ge,{variant:"outline-danger",size:"sm",icon:"bi-trash",onClick:mt(x=>A(l)(m.id),["stop"])},null,8,["onClick"])])],10,Lu))),128))]))])])]))}},qu=it(Ku,[["__scopeId","data-v-1ba576c0"]]);function ar(){const e=Z({id:1,firstName:"John",lastName:"Doe",role:"teaching_coach",email:"<EMAIL>"}),t=re(()=>e.value.role==="teaching_coach"),n=re(()=>e.value.role==="teacher"),s=re(()=>t.value),o=re(()=>!0),i=re(()=>t.value),r=re(()=>n.value),l=re(()=>!0),c=re(()=>!0);return{currentUser:e,isTeachingCoach:t,isTeacher:n,canCreateQuestions:s,canAnswerQuestions:o,canCreateActions:i,canCompleteActions:r,canWriteNotes:l,canCreateFloatingNotes:c,switchRole:a=>{e.value.role=a}}}const Qu={class:"role-switcher"},Wu={class:"dropdown"},zu={class:"dropdown-menu"},Gu={class:"dropdown-item-text"},Ju={class:"text-muted"},Yu={__name:"RoleSwitcher",setup(e){const{currentUser:t,switchRole:n}=ar();return(s,o)=>(C(),N("div",Qu,[v("div",Wu,[K(ge,{variant:"outline-light",size:"sm",class:"dropdown-toggle","data-bs-toggle":"dropdown","aria-expanded":"false",icon:"bi-person-gear"},{default:se(()=>[ye(Q(A(t).role==="teaching_coach"?"Teaching Coach":"Teacher"),1)]),_:1}),v("ul",zu,[v("li",null,[v("a",{class:Me(["dropdown-item",{active:A(t).role==="teaching_coach"}]),href:"#",onClick:o[0]||(o[0]=mt(i=>A(n)("teaching_coach"),["prevent"]))},o[2]||(o[2]=[v("i",{class:"bi bi-mortarboard me-2"},null,-1),ye(" Teaching Coach ")]),2)]),v("li",null,[v("a",{class:Me(["dropdown-item",{active:A(t).role==="teacher"}]),href:"#",onClick:o[1]||(o[1]=mt(i=>A(n)("teacher"),["prevent"]))},o[3]||(o[3]=[v("i",{class:"bi bi-person me-2"},null,-1),ye(" Teacher ")]),2)]),o[4]||(o[4]=v("li",null,[v("hr",{class:"dropdown-divider"})],-1)),v("li",null,[v("span",Gu,[v("small",Ju," Current: "+Q(A(t).firstName)+" "+Q(A(t).lastName),1)])])])])]))}},Xu=it(Yu,[["__scopeId","data-v-35f5150f"]]),Zu={class:"app-container"},ef={class:"navbar navbar-expand-lg navbar-dark bg-secondary"},tf={class:"container-fluid"},nf={class:"navbar-brand"},sf={class:"d-flex align-items-center gap-2"},of={key:0,class:"badge bg-danger ms-1"},rf={key:1,class:"container-fluid py-4 bg-light min-vh-100"},lf={class:"row g-3"},cf={class:"col-md-6"},af={class:"col-md-6"},uf={class:"col-md-6"},ff={class:"col-md-6"},df={__name:"App",setup(e){const t=Ks(),n=Z(null),s=Z(!1),{unreadCount:o}=cr();Wt("selectedClass",n),Wt("selectClass",l=>{n.value=l});const r=(l,c)=>{t.push(`/document/${l}/${c}`),s.value=!1};return(l,c)=>{const f=Ys("router-link"),a=Ys("router-view");return C(),N("div",Zu,[v("header",ef,[v("div",tf,[v("div",nf,[K(f,{to:"/",class:"text-decoration-none text-white"},{default:se(()=>c[2]||(c[2]=[v("h1",{class:"h4 mb-0"},"topicus",-1)])),_:1,__:[2]})]),v("nav",sf,[K(ge,{variant:"outline-light",size:"sm",icon:"bi-bell","aria-label":"Notifications",onClick:c[0]||(c[0]=u=>s.value=!s.value)},{default:se(()=>[A(o)>0?(C(),N("span",of,Q(A(o)),1)):le("",!0)]),_:1}),K(Xu)])])]),l.$route.name==="DocumentView"?(C(),me(a,{key:0})):le("",!0),!l.$route.name||l.$route.name!=="DocumentView"?(C(),N("main",rf,[v("div",lf,[v("div",cf,[K(An,{title:"Classes","add-button-text":"Add Class",type:"classes"})]),v("div",af,[K(An,{title:n.value?`Students - ${n.value.name}`:"Students - Select a class","add-button-text":"Add Student",type:"students"},null,8,["title"])]),v("div",uf,[K(An,{title:n.value?`Documents - ${n.value.name}`:"Documents - Select a class","add-button-text":"Add Document",type:"documents"},null,8,["title"])]),v("div",ff,[K(An,{title:n.value?`Statistics - ${n.value.name}`:"Statistics - Select a class",type:"statistics"},null,8,["title"])])])])):le("",!0),s.value?(C(),me(qu,{key:2,onClose:c[1]||(c[1]=u=>s.value=!1),onDocumentClicked:r})):le("",!0)])}}};function hf(e,t){const n=Z({id:t,title:"Class 5 May 2025",classId:e,lastEdited:new Date().toLocaleDateString("en-GB"),editor:"Mr. Smith"}),s=Z([{id:1,question:"Is every student nice to each other?",answer:"yes",note:"Can you elaborate on this"},{id:2,question:"How are you controlling the class when they are too loud?",answer:"fix that",note:""},{id:3,question:"Did anything notable happen recently?",answer:"Jimmy is still eating his boogers",note:""}]),o=Z([{id:1,subject:"Math",question:"Has the school norm been achieved?",answer:"yes",note:""},{id:2,subject:"Math",question:"Who is good at math?",answer:"not Jimmy",note:""},{id:3,subject:"Math",question:"Ho do you teach math?",answer:"book",note:""}]),i=Z([{id:1,student:"Jimmy",question:"How is Jimmy's reading",answer:"yes",note:""},{id:2,student:"Jimmy",question:"Is Jimmy getting bullied",answer:"he should be",note:""},{id:3,student:"Jimmy",question:"Anything else?",answer:"nobody likes him",note:""}]),r=Z(["Reading","Dutch","English","Math","PE"]),l=Z(["brock","Ash","Pikachu","Jimmy","girl name"]),c=Z("Math"),f=Z("Jimmy"),a=Z([{id:1,text:"Please be more specific",author:"Mr. Smith",timestamp:new Date}]),u=Z([{id:1,text:"Pay more attention to Jimmy",completed:!0,createdBy:"teaching_coach"}]),p=Z([]),m=re(()=>o.value.filter(z=>z.subject===c.value)),x=re(()=>i.value.filter(z=>z.student===f.value));return{document:n,overallQuestions:s,subjectQuestions:o,studentQuestions:i,subjects:r,students:l,selectedSubject:c,selectedStudent:f,generalNotes:a,actions:u,floatingNotes:p,filteredSubjectQuestions:m,filteredStudentQuestions:x,addOverallQuestion:z=>{const Y={id:Date.now(),question:z,answer:"",note:""};return s.value.push(Y),Y},addSubjectQuestion:z=>{const Y={id:Date.now(),subject:c.value,question:z,answer:"",note:""};return o.value.push(Y),Y},addStudentQuestion:z=>{const Y={id:Date.now(),student:f.value,question:z,answer:"",note:""};return i.value.push(Y),Y},updateAnswer:(z,Y,we)=>{let Ce;switch(z){case"overall":Ce=s.value;break;case"subject":Ce=o.value;break;case"student":Ce=i.value;break}const xe=Ce.find(_e=>_e.id===Y);xe&&(xe.answer=we)},addGeneralNote:z=>{const Y={id:Date.now(),text:z,author:"Current User",timestamp:new Date};return a.value.push(Y),Y},addAction:z=>{const Y={id:Date.now(),text:z,completed:!1,createdBy:"teaching_coach"};return u.value.push(Y),Y},toggleAction:z=>{const Y=u.value.find(we=>we.id===z);Y&&(Y.completed=!Y.completed)},addFloatingNote:(z,Y,we,Ce)=>{const xe={id:Date.now(),targetType:z,targetId:Y,text:we,position:Ce,author:"Current User",timestamp:new Date};return p.value.push(xe),xe},saveDocument:async()=>{try{return console.log("Saving document...",{document:n.value,overallQuestions:s.value,subjectQuestions:o.value,studentQuestions:i.value,generalNotes:a.value,actions:u.value,floatingNotes:p.value}),n.value.lastEdited=new Date().toLocaleDateString("en-GB"),{success:!0,message:"Document saved successfully!"}}catch(z){return{success:!1,message:"Failed to save document: "+z.message}}}}}const pf={class:"d-flex justify-content-between align-items-center w-100"},mf={key:0,class:"dropdown"},gf=["value"],vf=["value"],_f={class:"table-responsive"},yf={class:"table table-borderless"},bf=["onClick"],wf=["value","onInput","onClick"],xf=["onClick"],Cf=["onClick"],Sf={key:0,class:"mt-3"},Af={class:"input-group"},$f={__name:"QuestionSection",props:{title:{type:String,required:!0},questions:{type:Array,default:()=>[]},canCreate:{type:Boolean,default:!1},canAnswer:{type:Boolean,default:!0},questionType:{type:String,required:!0},color:{type:String,default:"pink",validator:e=>["pink","purple"].includes(e)},showDropdown:{type:Boolean,default:!1},dropdownOptions:{type:Array,default:()=>[]},dropdownValue:{type:String,default:""},dropdownLabel:{type:String,default:"Selection"}},emits:["add-question","update-answer","add-floating-note","dropdown-change"],setup(e,{emit:t}){const n=e,s=t,o=Z(""),i=()=>{o.value.trim()&&(s("add-question",o.value.trim()),o.value="")},r=(c,f)=>{s("update-answer",n.questionType,c,f)},l=(c,f,a)=>{const u=a.target.getBoundingClientRect(),p={x:u.left+window.scrollX,y:u.top+window.scrollY+u.height};console.log("Text clicked:",c,f,p),s("add-floating-note",c,f,"",p)};return(c,f)=>(C(),me(_t,{class:"mb-4"},{header:se(()=>[v("div",pf,[v("h2",{class:Me(["h5 mb-0 text-white",`bg-${e.color}`])},Q(e.title),3),e.showDropdown?(C(),N("div",mf,[v("select",{value:e.dropdownValue,onChange:f[0]||(f[0]=a=>c.$emit("dropdown-change",a.target.value)),class:"form-select form-select-sm",style:{"min-width":"150px"}},[(C(!0),N(de,null,Le(e.dropdownOptions,a=>(C(),N("option",{key:a,value:a},Q(a),9,vf))),128))],40,gf)])):le("",!0)])]),default:se(()=>[v("div",_f,[v("table",yf,[f[2]||(f[2]=v("thead",null,[v("tr",null,[v("th",{style:{width:"5%"}},"#"),v("th",{style:{width:"40%"}},"Question"),v("th",{style:{width:"40%"}},"Answer"),v("th",{style:{width:"15%"}},"Note")])],-1)),v("tbody",null,[(C(!0),N(de,null,Le(e.questions,(a,u)=>(C(),N("tr",{key:a.id},[v("td",null,Q(u+1),1),v("td",null,[v("div",{class:"question-text",onClick:p=>l("question",a.id,p)},Q(a.question),9,bf)]),v("td",null,[e.canAnswer?(C(),N("input",{key:0,value:a.answer,onInput:p=>r(a.id,p.target.value),onClick:p=>l("answer",a.id,p),class:"form-control form-control-sm",placeholder:"Enter answer..."},null,40,wf)):(C(),N("div",{key:1,class:"answer-text",onClick:p=>l("answer",a.id,p)},Q(a.answer||"No answer yet"),9,xf))]),v("td",null,[v("div",{class:"note-text",onClick:p=>l("note",a.id,p)},Q(a.note||""),9,Cf)])]))),128))])])]),e.canCreate?(C(),N("div",Sf,[v("div",Af,[tt(v("input",{"onUpdate:modelValue":f[1]||(f[1]=a=>o.value=a),onKeyup:gn(i,["enter"]),class:"form-control",placeholder:"Add new question..."},null,544),[[st,o.value]]),K(ge,{onClick:i,variant:"purple",icon:"bi-plus-lg"},{default:se(()=>f[3]||(f[3]=[ye(" Add ")])),_:1,__:[3]})])])):le("",!0)]),_:1}))}},fs=it($f,[["__scopeId","data-v-50fe4114"]]),Ef={class:"notes-list"},Rf={key:0,class:"text-center text-muted py-3"},kf={key:1},Pf={class:"note-content"},Tf={class:"note-text mb-1"},Nf={class:"text-muted"},Of={key:0,class:"mt-3"},If={class:"mb-2"},Mf=["onKeyup"],Df={class:"d-flex justify-content-end"},Ff={__name:"GeneralNotes",props:{notes:{type:Array,default:()=>[]},canWrite:{type:Boolean,default:!0}},emits:["add-note"],setup(e,{emit:t}){const n=t,s=Z(""),o=()=>{s.value.trim()&&(n("add-note",s.value.trim()),s.value="")},i=r=>{const c=new Date-r,f=Math.floor(c/(1e3*60)),a=Math.floor(c/(1e3*60*60)),u=Math.floor(c/(1e3*60*60*24));return f<1?"just now":f<60?`${f}m ago`:a<24?`${a}h ago`:u<7?`${u}d ago`:r.toLocaleDateString()};return(r,l)=>(C(),me(_t,{title:"General Notes",class:"mb-4"},{default:se(()=>[v("div",Ef,[e.notes.length===0?(C(),N("div",Rf,l[1]||(l[1]=[v("i",{class:"bi bi-sticky fs-1 mb-2"},null,-1),v("p",{class:"mb-0"},"No notes yet",-1)]))):(C(),N("div",kf,[(C(!0),N(de,null,Le(e.notes,c=>(C(),N("div",{key:c.id,class:"note-item"},[v("div",Pf,[v("p",Tf,Q(c.text),1),v("small",Nf," by "+Q(c.author)+" • "+Q(i(c.timestamp)),1)])]))),128))]))]),e.canWrite?(C(),N("div",Of,[v("div",If,[tt(v("textarea",{"onUpdate:modelValue":l[0]||(l[0]=c=>s.value=c),onKeyup:gn(mt(o,["ctrl"]),["enter"]),class:"form-control",rows:"3",placeholder:"Add a general note... (Ctrl+Enter to save)"},null,40,Mf),[[st,s.value]])]),v("div",Df,[K(ge,{onClick:o,variant:"purple",size:"sm",icon:"bi-plus-lg",disabled:!s.value.trim()},{default:se(()=>l[2]||(l[2]=[ye(" Add Note ")])),_:1,__:[2]},8,["disabled"])])])):le("",!0)]),_:1}))}},jf=it(Ff,[["__scopeId","data-v-4a0381cd"]]),Lf={class:"actions-list"},Bf={key:0,class:"text-center text-muted py-3"},Hf={key:1},Vf={class:"form-check"},Uf=["id","checked","disabled","onChange"],Kf=["for"],qf={class:"action-meta"},Qf={class:"text-muted"},Wf={key:0,class:"mt-3"},zf={class:"mb-2"},Gf={class:"d-flex justify-content-end"},Jf={key:1,class:"mt-3"},Yf={__name:"ActionChecklist",props:{actions:{type:Array,default:()=>[]},canCreate:{type:Boolean,default:!1},canComplete:{type:Boolean,default:!0}},emits:["add-action","toggle-action"],setup(e,{emit:t}){const n=t,s=Z(""),o=()=>{s.value.trim()&&(n("add-action",s.value.trim()),s.value="")},i=r=>{n("toggle-action",r)};return(r,l)=>(C(),me(_t,{title:"Actions",class:"mb-4"},{default:se(()=>[v("div",Lf,[e.actions.length===0?(C(),N("div",Bf,l[1]||(l[1]=[v("i",{class:"bi bi-list-check fs-1 mb-2"},null,-1),v("p",{class:"mb-0"},"No actions yet",-1)]))):(C(),N("div",Hf,[(C(!0),N(de,null,Le(e.actions,c=>(C(),N("div",{key:c.id,class:"action-item"},[v("div",Vf,[v("input",{id:`action-${c.id}`,checked:c.completed,disabled:!e.canComplete&&!c.completed,onChange:f=>i(c.id),class:"form-check-input",type:"checkbox"},null,40,Uf),v("label",{for:`action-${c.id}`,class:Me(["form-check-label",{"text-decoration-line-through text-muted":c.completed}])},Q(c.text),11,Kf)]),v("div",qf,[v("small",Qf," Created by "+Q(c.createdBy==="teaching_coach"?"Teaching Coach":"Teacher"),1)])]))),128))]))]),e.canCreate?(C(),N("div",Wf,[v("div",zf,[tt(v("input",{"onUpdate:modelValue":l[0]||(l[0]=c=>s.value=c),onKeyup:gn(o,["enter"]),class:"form-control",placeholder:"Add new action item..."},null,544),[[st,s.value]])]),v("div",Gf,[K(ge,{onClick:o,variant:"purple",size:"sm",icon:"bi-plus-lg",disabled:!s.value.trim()},{default:se(()=>l[2]||(l[2]=[ye(" Add Action ")])),_:1,__:[2]},8,["disabled"])])])):le("",!0),!e.canCreate&&e.canComplete?(C(),N("div",Jf,l[3]||(l[3]=[v("small",{class:"text-muted"},[v("i",{class:"bi bi-info-circle me-1"}),ye(" You can mark actions as complete but cannot create new ones. ")],-1)]))):le("",!0)]),_:1}))}},Xf=it(Yf,[["__scopeId","data-v-da8c8c9d"]]),Zf={class:"floating-note-header"},ed={class:"text-muted"},td={class:"floating-note-actions"},nd={class:"floating-note-body"},sd=["onKeyup"],od={key:1,class:"note-text mb-0"},id={key:0,class:"floating-note-footer"},rd={class:"d-flex justify-content-end gap-2"},ld={__name:"FloatingNote",props:{note:{type:Object,required:!0}},emits:["update","remove"],setup(e,{emit:t}){const n=e,s=t,o=Z(!1),i=Z(""),r=Z(null),l=()=>{i.value=n.note.text,o.value=!0,Fs(()=>{r.value&&r.value.focus()})},c=()=>{i.value.trim()&&(s("update",n.note.id,i.value.trim()),o.value=!1)},f=()=>{i.value="",o.value=!1},a=u=>{const m=new Date-u,x=Math.floor(m/(1e3*60)),$=Math.floor(m/(1e3*60*60));return x<1?"just now":x<60?`${x}m ago`:$<24?`${$}h ago`:u.toLocaleDateString()};return(u,p)=>(C(),N("div",{class:Me(["floating-note",{editing:o.value}])},[v("div",Zf,[v("small",ed,Q(e.note.author)+" • "+Q(a(e.note.timestamp)),1),v("div",td,[o.value?le("",!0):(C(),me(ge,{key:0,variant:"outline-secondary",size:"sm",icon:"bi-pencil",onClick:l})),K(ge,{variant:"outline-danger",size:"sm",icon:"bi-x-lg",onClick:p[0]||(p[0]=m=>u.$emit("remove"))})])]),v("div",nd,[o.value?tt((C(),N("textarea",{key:0,"onUpdate:modelValue":p[1]||(p[1]=m=>i.value=m),onKeyup:[gn(mt(c,["ctrl"]),["enter"]),gn(f,["esc"])],class:"form-control form-control-sm",rows:"3",placeholder:"Enter note text...",ref_key:"textareaRef",ref:r},null,40,sd)),[[st,i.value]]):(C(),N("p",od,Q(e.note.text),1))]),o.value?(C(),N("div",id,[v("div",rd,[K(ge,{variant:"secondary",size:"sm",onClick:f},{default:se(()=>p[2]||(p[2]=[ye(" Cancel ")])),_:1,__:[2]}),K(ge,{variant:"purple",size:"sm",onClick:c,disabled:!i.value.trim()},{default:se(()=>p[3]||(p[3]=[ye(" Save ")])),_:1,__:[3]},8,["disabled"])]),p[4]||(p[4]=v("small",{class:"text-muted"},"Ctrl+Enter to save, Esc to cancel",-1))])):le("",!0),p[5]||(p[5]=v("div",{class:"connection-line"},null,-1))],2))}},cd=it(ld,[["__scopeId","data-v-311d90dd"]]),ad={class:"document-view"},ud={class:"document-header"},fd={class:"container-fluid"},dd={class:"d-flex justify-content-between align-items-center"},hd={class:"d-flex align-items-center"},pd={class:"h4 mb-0"},md={class:"d-flex align-items-center gap-2"},gd={class:"text-muted"},vd={class:"container-fluid py-4"},_d={class:"row g-4"},yd={class:"col-lg-8"},bd={class:"col-lg-4"},wd={__name:"DocumentView",props:{classId:{type:[String,Number],required:!0},documentId:{type:[String,Number],required:!0}},setup(e){const t=e,n=Ks(),{document:s,overallQuestions:o,filteredSubjectQuestions:i,filteredStudentQuestions:r,subjects:l,students:c,selectedSubject:f,selectedStudent:a,generalNotes:u,actions:p,floatingNotes:m,addOverallQuestion:x,addSubjectQuestion:$,addStudentQuestion:V,updateAnswer:F,addGeneralNote:M,addAction:L,toggleAction:I,addFloatingNote:X,saveDocument:ve}=hf(t.classId,t.documentId),{canCreateQuestions:ie,canAnswerQuestions:z,canCreateActions:Y,canCompleteActions:we,canWriteNotes:Ce}=ar(),xe=Z(!1),_e=Z(null),Rt=()=>{n.push("/")},yt=async()=>{xe.value=!0,_e.value=null;try{const pe=await ve();_e.value={type:pe.success?"success":"error",text:pe.message},setTimeout(()=>{_e.value=null},3e3)}catch{_e.value={type:"error",text:"An unexpected error occurred while saving."}}finally{xe.value=!1}};return Ci(()=>{console.log("Document view mounted for class:",t.classId,"document:",t.documentId)}),(pe,G)=>(C(),N("div",ad,[v("header",ud,[v("div",fd,[v("div",dd,[v("div",hd,[K(ge,{variant:"outline-secondary",size:"sm",icon:"bi-arrow-left",onClick:Rt,class:"me-3"},{default:se(()=>G[2]||(G[2]=[ye(" Back ")])),_:1,__:[2]}),v("h1",pd,Q(A(s).title),1)]),v("div",md,[v("small",gd,"Last edited: "+Q(A(s).lastEdited),1),K(ge,{variant:"purple",icon:"bi-save",onClick:yt,disabled:xe.value},{default:se(()=>[ye(Q(xe.value?"Saving...":"Save"),1)]),_:1},8,["disabled"])])])])]),v("main",vd,[v("div",_d,[v("div",yd,[K(fs,{title:"Overall class questions",questions:A(o),"can-create":A(ie),"can-answer":A(z),"question-type":"overall",color:"pink",onAddQuestion:A(x),onUpdateAnswer:A(F),onAddFloatingNote:A(X)},null,8,["questions","can-create","can-answer","onAddQuestion","onUpdateAnswer","onAddFloatingNote"]),K(fs,{title:`${A(f)}`,questions:A(i),"can-create":A(ie),"can-answer":A(z),"question-type":"subject",color:"purple","show-dropdown":!0,"dropdown-options":A(l),"dropdown-value":A(f),"dropdown-label":"Subject Selection",onDropdownChange:G[0]||(G[0]=W=>f.value=W),onAddQuestion:A($),onUpdateAnswer:A(F),onAddFloatingNote:A(X)},null,8,["title","questions","can-create","can-answer","dropdown-options","dropdown-value","onAddQuestion","onUpdateAnswer","onAddFloatingNote"]),K(fs,{title:`${A(a)} Studentname`,questions:A(r),"can-create":A(ie),"can-answer":A(z),"question-type":"student",color:"pink","show-dropdown":!0,"dropdown-options":A(c),"dropdown-value":A(a),"dropdown-label":"Student Selection",onDropdownChange:G[1]||(G[1]=W=>a.value=W),onAddQuestion:A(V),onUpdateAnswer:A(F),onAddFloatingNote:A(X)},null,8,["title","questions","can-create","can-answer","dropdown-options","dropdown-value","onAddQuestion","onUpdateAnswer","onAddFloatingNote"])]),v("div",bd,[K(jf,{notes:A(u),"can-write":A(Ce),onAddNote:A(M)},null,8,["notes","can-write","onAddNote"]),K(Xf,{actions:A(p),"can-create":A(Y),"can-complete":A(we),onAddAction:A(L),onToggleAction:A(I)},null,8,["actions","can-create","can-complete","onAddAction","onToggleAction"])])])]),(C(!0),N(de,null,Le(A(m),W=>(C(),me(cd,{key:W.id,note:W,style:Hn({top:W.position.y+"px",left:W.position.x+"px"})},null,8,["note","style"]))),128)),_e.value?(C(),N("div",{key:0,class:Me(["alert",_e.value.type==="success"?"alert-success":"alert-danger","save-message"])},Q(_e.value.text),3)):le("",!0)]))}},xd=it(wd,[["__scopeId","data-v-202b7744"]]),Cd=[{path:"/document/:classId/:documentId",name:"DocumentView",component:xd,props:!0}],Sd=Na({history:la("/Topicus/"),routes:Cd}),ur=Rc(df);ur.use(Sd);ur.mount("#app");
