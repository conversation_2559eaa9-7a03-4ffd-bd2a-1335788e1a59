<template>
  <BaseCard title="Actions" class="mb-4">
    <!-- Actions List -->
    <div class="actions-list">
      <div v-if="actions.length === 0" class="text-center text-muted py-3">
        <i class="bi bi-list-check fs-1 mb-2"></i>
        <p class="mb-0">No actions yet</p>
      </div>
      
      <div v-else>
        <div
          v-for="action in actions"
          :key="action.id"
          class="action-item"
        >
          <div class="form-check">
            <input
              :id="`action-${action.id}`"
              :checked="action.completed"
              :disabled="!canComplete && !action.completed"
              @change="handleToggleAction(action.id)"
              class="form-check-input"
              type="checkbox"
            />
            <label
              :for="`action-${action.id}`"
              :class="[
                'form-check-label',
                { 'text-decoration-line-through text-muted': action.completed }
              ]"
            >
              {{ action.text }}
            </label>
          </div>
          <div class="action-meta">
            <small class="text-muted">
              Created by {{ action.createdBy === 'teaching_coach' ? 'Teaching Coach' : 'Teacher' }}
            </small>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Action Input -->
    <div v-if="canCreate" class="mt-3">
      <div class="mb-2">
        <input
          v-model="newActionText"
          @keyup.enter="handleAddAction"
          class="form-control"
          placeholder="Add new action item..."
        />
      </div>
      <div class="d-flex justify-content-end">
        <BaseButton
          @click="handleAddAction"
          variant="purple"
          size="sm"
          icon="bi-plus-lg"
          :disabled="!newActionText.trim()"
        >
          Add Action
        </BaseButton>
      </div>
    </div>

    <!-- Info for teachers -->
    <div v-if="!canCreate && canComplete" class="mt-3">
      <small class="text-muted">
        <i class="bi bi-info-circle me-1"></i>
        You can mark actions as complete but cannot create new ones.
      </small>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref } from 'vue'
import BaseCard from './ui/BaseCard.vue'
import BaseButton from './ui/BaseButton.vue'

const props = defineProps({
  actions: {
    type: Array,
    default: () => []
  },
  canCreate: {
    type: Boolean,
    default: false
  },
  canComplete: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['add-action', 'toggle-action'])

const newActionText = ref('')

const handleAddAction = () => {
  if (newActionText.value.trim()) {
    emit('add-action', newActionText.value.trim())
    newActionText.value = ''
  }
}

const handleToggleAction = (actionId) => {
  emit('toggle-action', actionId)
}
</script>

<style scoped>
.actions-list {
  max-height: 300px;
  overflow-y: auto;
}

.action-item {
  padding: 0.75rem;
  border-bottom: 1px solid #f8f9fa;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  border-left: 3px solid #28a745;
}

.action-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-check {
  margin-bottom: 0.25rem;
}

.form-check-input {
  margin-top: 0.125rem;
}

.form-check-label {
  cursor: pointer;
  line-height: 1.4;
  margin-left: 0.25rem;
}

.action-meta {
  margin-left: 1.5rem;
}

.form-check-input:checked {
  background-color: #28a745;
  border-color: #28a745;
}

.form-check-input:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}
</style>
