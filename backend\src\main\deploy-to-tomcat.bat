@echo off
echo ========================================
echo Topicus Dashboard - Tomcat Deployment
echo ========================================

REM Check if CATALINA_HOME is set
if "%CATALINA_HOME%"=="" (
    echo ERROR: CATALINA_HOME environment variable is not set!
    echo Please set CATALINA_HOME to your Tomcat installation directory.
    echo Example: set CATALINA_HOME=C:\apache-tomcat-9.0.xx
    pause
    exit /b 1
)

echo CATALINA_HOME: %CATALINA_HOME%
echo.

REM Navigate to webapp directory
cd /d "%~dp0webapp"
if errorlevel 1 (
    echo ERROR: Could not navigate to webapp directory
    pause
    exit /b 1
)

echo Building Vue.js application...
call npm run build
if errorlevel 1 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo Build completed successfully!
echo.

REM Stop Tomcat
echo Stopping Tomcat...
call "%CATALINA_HOME%\bin\shutdown.bat"
timeout /t 5 /nobreak >nul

REM Remove existing deployment
if exist "%CATALINA_HOME%\webapps\Topicus" (
    echo Removing existing Topicus deployment...
    rmdir /s /q "%CATALINA_HOME%\webapps\Topicus"
)

REM Copy new deployment
echo Deploying to Tomcat...
xcopy /E /I "Topicus" "%CATALINA_HOME%\webapps\Topicus"
if errorlevel 1 (
    echo ERROR: Deployment failed!
    pause
    exit /b 1
)

echo Deployment completed successfully!
echo.

REM Start Tomcat
echo Starting Tomcat...
call "%CATALINA_HOME%\bin\startup.bat"

echo.
echo ========================================
echo Deployment Complete!
echo ========================================
echo.
echo The application will be available at:
echo http://localhost:8080/Topicus/
echo.
echo Wait a few seconds for Tomcat to start, then open the URL in your browser.
echo.
pause
