package org.example.module4topicus;

import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.sql.*;
import java.util.Map;
import java.util.HashMap;

@Path("/students")
public class StudentResource {
    
    private static final String DB_URL = "**************************************************";
    private static final String DB_USER = "dab_di2425-2b_280";
    private static final String DB_PASSWORD = "dab_di2425-2b_280";
    
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response addStudent(Map<String, String> studentData) {
        if (studentData == null || !studentData.containsKey("firstName") || 
            !studentData.containsKey("lastName") || !studentData.containsKey("classId")) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("Missing required fields: firstName, lastName, classId")
                    .build();
        }
        
        String firstName = studentData.get("firstName");
        String lastName = studentData.get("lastName");
        int classId = Integer.parseInt(studentData.get("classId"));
        
        try {
            int studentId = addStudentToDatabase(firstName, lastName, classId);
            Map<String, Object> result = new HashMap<>();
            result.put("id", studentId);
            result.put("firstName", firstName);
            result.put("lastName", lastName);
            result.put("classId", classId);
            result.put("message", "Student added successfully");
            
            return Response.status(Response.Status.CREATED)
                    .entity(result)
                    .build();
        } catch (Exception e) {
            e.printStackTrace();
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("Failed to add student: " + e.getMessage())
                    .build();
        }
    }
    
    private int addStudentToDatabase(String firstName, String lastName, int classId) throws SQLException {
        try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
            // First check if the class exists
            String checkClassQuery = "SELECT id FROM class WHERE id = ?;";
            try (PreparedStatement checkClass = conn.prepareStatement(checkClassQuery)) {
                checkClass.setInt(1, classId);
                try (ResultSet classResult = checkClass.executeQuery()) {
                    if (!classResult.next()) {
                        throw new RuntimeException("Class with ID " + classId + " does not exist");
                    }
                }
            }
            
            // Insert the student
            String query = "INSERT INTO student (first_name, last_name, class_id) VALUES (?, ?, ?) RETURNING id;";
            try (PreparedStatement statement = conn.prepareStatement(query)) {
                statement.setString(1, firstName);
                statement.setString(2, lastName);
                statement.setInt(3, classId);
                
                try (ResultSet resultSet = statement.executeQuery()) {
                    if (resultSet.next()) {
                        return resultSet.getInt("id");
                    } else {
                        throw new SQLException("Failed to retrieve generated ID");
                    }
                }
            }
        }
    }
}




