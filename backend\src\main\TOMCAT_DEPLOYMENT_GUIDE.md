# Tomcat Deployment Guide for Topicus Dashboard

## Overview

This guide explains how to deploy the Vue.js Topicus Dashboard application to Apache Tomcat server.

## Prerequisites

1. **Apache Tomcat 9.0+** installed and running
2. **Java 8+** installed
3. **Node.js and npm** (for building the frontend)

## Deployment Steps

### Step 1: Build the Vue.js Application

1. Navigate to the webapp source directory:
   ```bash
   cd backend/src/main/webapp
   ```

2. Install dependencies (if not already done):
   ```bash
   npm install
   ```

3. Build the production version:
   ```bash
   npm run build
   ```

### Step 2: Prepare Tomcat Deployment

The built application is already prepared in the correct structure:
```
backend/src/main/webapp/Topicus/
├── index.html                 # Main HTML file
├── assets/                    # Static assets
│   ├── index-[hash].js       # Main JavaScript bundle
│   ├── index-[hash].css      # Main CSS bundle
│   ├── favicon-[hash].ico    # Favicon
│   └── bootstrap-icons.*     # Font files
└── WEB-INF/
    └── web.xml               # Tomcat configuration
```

### Step 3: Deploy to Tomcat

#### Option A: Copy to Tomcat webapps directory

1. **Stop Tomcat** (if running):
   ```bash
   # Windows
   %CATALINA_HOME%\bin\shutdown.bat
   
   # Linux/Mac
   $CATALINA_HOME/bin/shutdown.sh
   ```

2. **Copy the Topicus folder** to Tomcat's webapps directory:
   ```bash
   # Windows
   xcopy /E /I "backend\src\main\webapp\Topicus" "%CATALINA_HOME%\webapps\Topicus"
   
   # Linux/Mac
   cp -r backend/src/main/webapp/Topicus $CATALINA_HOME/webapps/
   ```

3. **Start Tomcat**:
   ```bash
   # Windows
   %CATALINA_HOME%\bin\startup.bat
   
   # Linux/Mac
   $CATALINA_HOME/bin/startup.sh
   ```

#### Option B: Create WAR file (Alternative)

1. **Create WAR file**:
   ```bash
   cd backend/src/main/webapp
   jar -cvf Topicus.war -C Topicus .
   ```

2. **Deploy WAR file** to Tomcat:
   - Copy `Topicus.war` to `%CATALINA_HOME%\webapps\`
   - Tomcat will automatically extract it

### Step 4: Access the Application

Once deployed, access the application at:
```
http://localhost:8080/Topicus/
```

## Configuration Details

### Web.xml Configuration

The `web.xml` file includes:
- **Welcome file**: `index.html` as the default page
- **Error handling**: 404 errors redirect to `index.html` (for Vue Router)
- **MIME types**: Proper content types for modern web assets
- **Security**: Basic protection for WEB-INF directory

### Vue Router Configuration

The application uses Vue Router with:
- **Base path**: `/Topicus/` (configured in `vite.config.js`)
- **History mode**: HTML5 history API
- **Routes**:
  - `/` - Home page with dashboard
  - `/document/:classId/:documentId` - Document view page

## Backend Integration

### API Endpoints

The frontend expects the following API endpoints to be available:

```
GET  /api/classes                           # Get all classes
GET  /api/classes/:id/students              # Get students for class
GET  /api/classes/:id/documents             # Get documents for class
GET  /api/documents/:classId/:documentId    # Get specific document
PUT  /api/documents/:classId/:documentId    # Save document changes
POST /api/documents/:classId/:documentId/questions  # Add question
PUT  /api/documents/:classId/:documentId/questions/:id/answer  # Update answer
POST /api/documents/:classId/:documentId/notes      # Add note
POST /api/documents/:classId/:documentId/actions    # Add action
PUT  /api/documents/:classId/:documentId/actions/:id # Update action
```

### Database Integration

The application is designed to work with the existing `DBConnection.java` class. You'll need to:

1. **Create REST endpoints** that use `DBConnection.java`
2. **Add CORS headers** for frontend-backend communication
3. **Implement the API endpoints** listed above

### Sample Servlet Configuration

Add this to your `web.xml` or create separate servlet classes:

```xml
<servlet>
    <servlet-name>ApiServlet</servlet-name>
    <servlet-class>your.package.ApiServlet</servlet-class>
</servlet>
<servlet-mapping>
    <servlet-name>ApiServlet</servlet-name>
    <url-pattern>/api/*</url-pattern>
</servlet-mapping>
```

## Troubleshooting

### Common Issues

1. **404 on page refresh**:
   - Ensure `web.xml` has the error-page configuration
   - Check that Vue Router is using history mode

2. **Assets not loading**:
   - Verify the base path in `vite.config.js` matches deployment path
   - Check MIME type mappings in `web.xml`

3. **API calls failing**:
   - Ensure backend API endpoints are implemented
   - Check CORS configuration
   - Verify proxy settings in `vite.config.js`

### Development vs Production

- **Development**: Use `npm run dev` for hot reloading
- **Production**: Use `npm run build` and deploy to Tomcat

### Logs

Check Tomcat logs for deployment issues:
- `%CATALINA_HOME%\logs\catalina.out` (Linux/Mac)
- `%CATALINA_HOME%\logs\catalina.yyyy-mm-dd.log` (Windows)

## Security Considerations

1. **HTTPS**: Use HTTPS in production
2. **Authentication**: Implement proper user authentication
3. **CORS**: Configure CORS properly for your domain
4. **Input validation**: Validate all user inputs on the backend

## Performance Optimization

1. **Gzip compression**: Enable in Tomcat for better performance
2. **Caching**: Configure proper cache headers for static assets
3. **CDN**: Consider using a CDN for static assets in production

## Next Steps

1. **Implement backend API endpoints** using `DBConnection.java`
2. **Add authentication and authorization**
3. **Configure database connection pooling**
4. **Set up production environment with HTTPS**
5. **Implement proper logging and monitoring**
