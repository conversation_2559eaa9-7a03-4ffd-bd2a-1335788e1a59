<template>
  <div class="floating-note" :class="{ 'editing': isEditing }">
    <div class="floating-note-header">
      <small class="text-muted">
        {{ note.author }} • {{ formatTimestamp(note.timestamp) }}
      </small>
      <div class="floating-note-actions">
        <BaseButton
          v-if="!isEditing"
          variant="outline-secondary"
          size="sm"
          icon="bi-pencil"
          @click="startEditing"
        />
        <BaseButton
          variant="outline-danger"
          size="sm"
          icon="bi-x-lg"
          @click="$emit('remove')"
        />
      </div>
    </div>
    
    <div class="floating-note-body">
      <textarea
        v-if="isEditing"
        v-model="editText"
        @keyup.ctrl.enter="saveEdit"
        @keyup.esc="cancelEdit"
        class="form-control form-control-sm"
        rows="3"
        placeholder="Enter note text..."
        ref="textareaRef"
      ></textarea>
      <p v-else class="note-text mb-0">{{ note.text }}</p>
    </div>
    
    <div v-if="isEditing" class="floating-note-footer">
      <div class="d-flex justify-content-end gap-2">
        <BaseButton
          variant="secondary"
          size="sm"
          @click="cancelEdit"
        >
          Cancel
        </BaseButton>
        <BaseButton
          variant="purple"
          size="sm"
          @click="saveEdit"
          :disabled="!editText.trim()"
        >
          Save
        </BaseButton>
      </div>
      <small class="text-muted">Ctrl+Enter to save, Esc to cancel</small>
    </div>
    
    <!-- Connection line to target element -->
    <div class="connection-line"></div>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import BaseButton from './ui/BaseButton.vue'

const props = defineProps({
  note: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update', 'remove'])

const isEditing = ref(false)
const editText = ref('')
const textareaRef = ref(null)

const startEditing = () => {
  editText.value = props.note.text
  isEditing.value = true
  nextTick(() => {
    if (textareaRef.value) {
      textareaRef.value.focus()
    }
  })
}

const saveEdit = () => {
  if (editText.value.trim()) {
    emit('update', props.note.id, editText.value.trim())
    isEditing.value = false
  }
}

const cancelEdit = () => {
  editText.value = ''
  isEditing.value = false
}

const formatTimestamp = (timestamp) => {
  const now = new Date()
  const diff = now - timestamp
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))

  if (minutes < 1) return 'just now'
  if (minutes < 60) return `${minutes}m ago`
  if (hours < 24) return `${hours}h ago`
  return timestamp.toLocaleDateString()
}
</script>

<style scoped>
.floating-note {
  position: absolute;
  background: #fff;
  border: 2px solid #E8436E;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 250px;
  max-width: 350px;
  z-index: 1000;
  font-size: 0.875rem;
}

.floating-note.editing {
  min-width: 300px;
}

.floating-note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: #E8436E;
  color: white;
  border-radius: 6px 6px 0 0;
}

.floating-note-actions {
  display: flex;
  gap: 0.25rem;
}

.floating-note-body {
  padding: 0.75rem;
}

.floating-note-footer {
  padding: 0.5rem;
  border-top: 1px solid #f8f9fa;
  background-color: #f8f9fa;
  border-radius: 0 0 6px 6px;
}

.note-text {
  line-height: 1.4;
  color: #333;
  white-space: pre-wrap;
}

.connection-line {
  position: absolute;
  top: -10px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #E8436E;
}

.form-control:focus {
  border-color: #E8436E;
  box-shadow: 0 0 0 0.2rem rgba(232, 67, 110, 0.25);
}

/* Make floating notes draggable */
.floating-note {
  cursor: move;
}

.floating-note:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}
</style>
