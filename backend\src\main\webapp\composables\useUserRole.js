import { ref, computed } from 'vue'

export function useUserRole() {
  // Mock user role - in a real app this would come from authentication
  const currentUser = ref({
    id: 1,
    firstName: '<PERSON>',
    lastName: 'Doe',
    role: 'teaching_coach', // 'teaching_coach' or 'teacher'
    email: '<EMAIL>'
  })

  // Computed properties for role-based permissions
  const isTeachingCoach = computed(() => currentUser.value.role === 'teaching_coach')
  const isTeacher = computed(() => currentUser.value.role === 'teacher')

  // Permission checks
  const canCreateQuestions = computed(() => isTeachingCoach.value)
  const canAnswerQuestions = computed(() => true) // Both roles can answer
  const canCreateActions = computed(() => isTeachingCoach.value)
  const canCompleteActions = computed(() => isTeacher.value)
  const canWriteNotes = computed(() => true) // Both roles can write notes
  const canCreateFloatingNotes = computed(() => true) // Both roles can create floating notes

  // Function to switch user role (for testing purposes)
  const switchRole = (newRole) => {
    currentUser.value.role = newRole
  }

  return {
    currentUser,
    isTeachingCoach,
    isTeacher,
    canCreateQuestions,
    canAnswerQuestions,
    canCreateActions,
    canCompleteActions,
    canWriteNotes,
    canCreateFloatingNotes,
    switchRole
  }
}
