(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const r of o)if(r.type==="childList")for(const l of r.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&s(l)}).observe(document,{childList:!0,subtree:!0});function n(o){const r={};return o.integrity&&(r.integrity=o.integrity),o.referrerPolicy&&(r.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?r.credentials="include":o.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function s(o){if(o.ep)return;o.ep=!0;const r=n(o);fetch(o.href,r)}})();(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))n(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const r of o.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&n(r)}).observe(document,{childList:!0,subtree:!0});function t(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(s){if(s.ep)return;s.ep=!0;const o=t(s);fetch(s.href,o)}})();/**
* @vue/shared v3.5.15
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Ss(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const re={},Vt=[],Ze=()=>{},ul=()=>!1,Pn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),As=e=>e.startsWith("onUpdate:"),Ce=Object.assign,Ds=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},dl=Object.prototype.hasOwnProperty,Z=(e,t)=>dl.call(e,t),B=Array.isArray,Lt=e=>Tn(e)==="[object Map]",Uo=e=>Tn(e)==="[object Set]",Q=e=>typeof e=="function",ve=e=>typeof e=="string",vt=e=>typeof e=="symbol",ce=e=>e!==null&&typeof e=="object",Bo=e=>(ce(e)||Q(e))&&Q(e.then)&&Q(e.catch),qo=Object.prototype.toString,Tn=e=>qo.call(e),fl=e=>Tn(e).slice(8,-1),zo=e=>Tn(e)==="[object Object]",$s=e=>ve(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,tn=Ss(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),In=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},pl=/-(\w)/g,Be=In(e=>e.replace(pl,(t,n)=>n?n.toUpperCase():"")),hl=/\B([A-Z])/g,St=In(e=>e.replace(hl,"-$1").toLowerCase()),Rn=In(e=>e.charAt(0).toUpperCase()+e.slice(1)),Hn=In(e=>e?`on${Rn(e)}`:""),kt=(e,t)=>!Object.is(e,t),Sn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Qo=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},ds=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Js;const Vn=()=>Js||(Js=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ln(e){if(B(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],o=ve(s)?yl(s):Ln(s);if(o)for(const r in o)t[r]=o[r]}return t}else if(ve(e)||ce(e))return e}const ml=/;(?![^(]*\))/g,vl=/:([^]+)/,gl=/\/\*[^]*?\*\//g;function yl(e){const t={};return e.replace(gl,"").split(ml).forEach(n=>{if(n){const s=n.split(vl);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Ee(e){let t="";if(ve(e))t=e;else if(B(e))for(let n=0;n<e.length;n++){const s=Ee(e[n]);s&&(t+=s+" ")}else if(ce(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const bl="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",_l=Ss(bl);function Jo(e){return!!e||e===""}const Wo=e=>!!(e&&e.__v_isRef===!0),J=e=>ve(e)?e:e==null?"":B(e)||ce(e)&&(e.toString===qo||!Q(e.toString))?Wo(e)?J(e.value):JSON.stringify(e,Go,2):String(e),Go=(e,t)=>Wo(t)?Go(e,t.value):Lt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,o],r)=>(n[Xn(s,r)+" =>"]=o,n),{})}:Uo(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Xn(n))}:vt(t)?Xn(t):ce(t)&&!B(t)&&!zo(t)?String(t):t,Xn=(e,t="")=>{var n;return vt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.15
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let $e;class wl{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=$e,!t&&$e&&(this.index=($e.scopes||($e.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=$e;try{return $e=this,t()}finally{$e=n}}}on(){++this._on===1&&(this.prevScope=$e,$e=this)}off(){this._on>0&&--this._on===0&&($e=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function xl(){return $e}let ae;const Yn=new WeakSet;class Ko{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,$e&&$e.active&&$e.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Yn.has(this)&&(Yn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Xo(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Ws(this),Yo(this);const t=ae,n=qe;ae=this,qe=!0;try{return this.fn()}finally{Zo(this),ae=t,qe=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Os(t);this.deps=this.depsTail=void 0,Ws(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Yn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){fs(this)&&this.run()}get dirty(){return fs(this)}}let Ho=0,nn,sn;function Xo(e,t=!1){if(e.flags|=8,t){e.next=sn,sn=e;return}e.next=nn,nn=e}function Es(){Ho++}function js(){if(--Ho>0)return;if(sn){let t=sn;for(sn=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;nn;){let t=nn;for(nn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Yo(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Zo(e){let t,n=e.depsTail,s=n;for(;s;){const o=s.prevDep;s.version===-1?(s===n&&(n=o),Os(s),kl(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=o}e.deps=t,e.depsTail=n}function fs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(er(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function er(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===cn)||(e.globalVersion=cn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!fs(e))))return;e.flags|=2;const t=e.dep,n=ae,s=qe;ae=e,qe=!0;try{Yo(e);const o=e.fn(e._value);(t.version===0||kt(o,e._value))&&(e.flags|=128,e._value=o,t.version++)}catch(o){throw t.version++,o}finally{ae=n,qe=s,Zo(e),e.flags&=-3}}function Os(e,t=!1){const{dep:n,prevSub:s,nextSub:o}=e;if(s&&(s.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let r=n.computed.deps;r;r=r.nextDep)Os(r,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function kl(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let qe=!0;const tr=[];function ft(){tr.push(qe),qe=!1}function pt(){const e=tr.pop();qe=e===void 0?!0:e}function Ws(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ae;ae=void 0;try{t()}finally{ae=n}}}let cn=0;class Cl{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Ns{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ae||!qe||ae===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ae)n=this.activeLink=new Cl(ae,this),ae.deps?(n.prevDep=ae.depsTail,ae.depsTail.nextDep=n,ae.depsTail=n):ae.deps=ae.depsTail=n,nr(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=ae.depsTail,n.nextDep=void 0,ae.depsTail.nextDep=n,ae.depsTail=n,ae.deps===n&&(ae.deps=s)}return n}trigger(t){this.version++,cn++,this.notify(t)}notify(t){Es();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{js()}}}function nr(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)nr(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const ps=new WeakMap,jt=Symbol(""),hs=Symbol(""),un=Symbol("");function we(e,t,n){if(qe&&ae){let s=ps.get(e);s||ps.set(e,s=new Map);let o=s.get(n);o||(s.set(n,o=new Ns),o.map=s,o.key=n),o.track()}}function ut(e,t,n,s,o,r){const l=ps.get(e);if(!l){cn++;return}const a=i=>{i&&i.trigger()};if(Es(),t==="clear")l.forEach(a);else{const i=B(e),d=i&&$s(n);if(i&&n==="length"){const c=Number(s);l.forEach((f,h)=>{(h==="length"||h===un||!vt(h)&&h>=c)&&a(f)})}else switch((n!==void 0||l.has(void 0))&&a(l.get(n)),d&&a(l.get(un)),t){case"add":i?d&&a(l.get("length")):(a(l.get(jt)),Lt(e)&&a(l.get(hs)));break;case"delete":i||(a(l.get(jt)),Lt(e)&&a(l.get(hs)));break;case"set":Lt(e)&&a(l.get(jt));break}}js()}function Mt(e){const t=Y(e);return t===e?t:(we(t,"iterate",un),Le(e)?t:t.map(be))}function Un(e){return we(e=Y(e),"iterate",un),e}const Sl={__proto__:null,[Symbol.iterator](){return Zn(this,Symbol.iterator,be)},concat(...e){return Mt(this).concat(...e.map(t=>B(t)?Mt(t):t))},entries(){return Zn(this,"entries",e=>(e[1]=be(e[1]),e))},every(e,t){return at(this,"every",e,t,void 0,arguments)},filter(e,t){return at(this,"filter",e,t,n=>n.map(be),arguments)},find(e,t){return at(this,"find",e,t,be,arguments)},findIndex(e,t){return at(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return at(this,"findLast",e,t,be,arguments)},findLastIndex(e,t){return at(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return at(this,"forEach",e,t,void 0,arguments)},includes(...e){return es(this,"includes",e)},indexOf(...e){return es(this,"indexOf",e)},join(e){return Mt(this).join(e)},lastIndexOf(...e){return es(this,"lastIndexOf",e)},map(e,t){return at(this,"map",e,t,void 0,arguments)},pop(){return Xt(this,"pop")},push(...e){return Xt(this,"push",e)},reduce(e,...t){return Gs(this,"reduce",e,t)},reduceRight(e,...t){return Gs(this,"reduceRight",e,t)},shift(){return Xt(this,"shift")},some(e,t){return at(this,"some",e,t,void 0,arguments)},splice(...e){return Xt(this,"splice",e)},toReversed(){return Mt(this).toReversed()},toSorted(e){return Mt(this).toSorted(e)},toSpliced(...e){return Mt(this).toSpliced(...e)},unshift(...e){return Xt(this,"unshift",e)},values(){return Zn(this,"values",be)}};function Zn(e,t,n){const s=Un(e),o=s[t]();return s!==e&&!Le(e)&&(o._next=o.next,o.next=()=>{const r=o._next();return r.value&&(r.value=n(r.value)),r}),o}const Al=Array.prototype;function at(e,t,n,s,o,r){const l=Un(e),a=l!==e&&!Le(e),i=l[t];if(i!==Al[t]){const f=i.apply(e,r);return a?be(f):f}let d=n;l!==e&&(a?d=function(f,h){return n.call(this,be(f),h,e)}:n.length>2&&(d=function(f,h){return n.call(this,f,h,e)}));const c=i.call(l,d,s);return a&&o?o(c):c}function Gs(e,t,n,s){const o=Un(e);let r=n;return o!==e&&(Le(e)?n.length>3&&(r=function(l,a,i){return n.call(this,l,a,i,e)}):r=function(l,a,i){return n.call(this,l,be(a),i,e)}),o[t](r,...s)}function es(e,t,n){const s=Y(e);we(s,"iterate",un);const o=s[t](...n);return(o===-1||o===!1)&&Ps(n[0])?(n[0]=Y(n[0]),s[t](...n)):o}function Xt(e,t,n=[]){ft(),Es();const s=Y(e)[t].apply(e,n);return js(),pt(),s}const Dl=Ss("__proto__,__v_isRef,__isVue"),sr=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(vt));function $l(e){vt(e)||(e=String(e));const t=Y(this);return we(t,"has",e),t.hasOwnProperty(e)}class or{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const o=this._isReadonly,r=this._isShallow;if(n==="__v_isReactive")return!o;if(n==="__v_isReadonly")return o;if(n==="__v_isShallow")return r;if(n==="__v_raw")return s===(o?r?Rl:ir:r?ar:lr).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const l=B(t);if(!o){let i;if(l&&(i=Sl[n]))return i;if(n==="hasOwnProperty")return $l}const a=Reflect.get(t,n,ke(t)?t:s);return(vt(n)?sr.has(n):Dl(n))||(o||we(t,"get",n),r)?a:ke(a)?l&&$s(n)?a:a.value:ce(a)?o?ur(a):Ot(a):a}}class rr extends or{constructor(t=!1){super(!1,t)}set(t,n,s,o){let r=t[n];if(!this._isShallow){const i=Ct(r);if(!Le(s)&&!Ct(s)&&(r=Y(r),s=Y(s)),!B(t)&&ke(r)&&!ke(s))return i?!1:(r.value=s,!0)}const l=B(t)&&$s(n)?Number(n)<t.length:Z(t,n),a=Reflect.set(t,n,s,ke(t)?t:o);return t===Y(o)&&(l?kt(s,r)&&ut(t,"set",n,s):ut(t,"add",n,s)),a}deleteProperty(t,n){const s=Z(t,n);t[n];const o=Reflect.deleteProperty(t,n);return o&&s&&ut(t,"delete",n,void 0),o}has(t,n){const s=Reflect.has(t,n);return(!vt(n)||!sr.has(n))&&we(t,"has",n),s}ownKeys(t){return we(t,"iterate",B(t)?"length":jt),Reflect.ownKeys(t)}}class El extends or{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const jl=new rr,Ol=new El,Nl=new rr(!0),ms=e=>e,wn=e=>Reflect.getPrototypeOf(e);function Fl(e,t,n){return function(...s){const o=this.__v_raw,r=Y(o),l=Lt(r),a=e==="entries"||e===Symbol.iterator&&l,i=e==="keys"&&l,d=o[e](...s),c=n?ms:t?$n:be;return!t&&we(r,"iterate",i?hs:jt),{next(){const{value:f,done:h}=d.next();return h?{value:f,done:h}:{value:a?[c(f[0]),c(f[1])]:c(f),done:h}},[Symbol.iterator](){return this}}}}function xn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Ml(e,t){const n={get(s){const o=this.__v_raw,r=Y(o),l=Y(s);e||(kt(s,l)&&we(r,"get",s),we(r,"get",l));const{has:a}=wn(r),i=t?ms:e?$n:be;if(a.call(r,s))return i(o.get(s));if(a.call(r,l))return i(o.get(l));o!==r&&o.get(s)},get size(){const s=this.__v_raw;return!e&&we(Y(s),"iterate",jt),Reflect.get(s,"size",s)},has(s){const o=this.__v_raw,r=Y(o),l=Y(s);return e||(kt(s,l)&&we(r,"has",s),we(r,"has",l)),s===l?o.has(s):o.has(s)||o.has(l)},forEach(s,o){const r=this,l=r.__v_raw,a=Y(l),i=t?ms:e?$n:be;return!e&&we(a,"iterate",jt),l.forEach((d,c)=>s.call(o,i(d),i(c),r))}};return Ce(n,e?{add:xn("add"),set:xn("set"),delete:xn("delete"),clear:xn("clear")}:{add(s){!t&&!Le(s)&&!Ct(s)&&(s=Y(s));const o=Y(this);return wn(o).has.call(o,s)||(o.add(s),ut(o,"add",s,s)),this},set(s,o){!t&&!Le(o)&&!Ct(o)&&(o=Y(o));const r=Y(this),{has:l,get:a}=wn(r);let i=l.call(r,s);i||(s=Y(s),i=l.call(r,s));const d=a.call(r,s);return r.set(s,o),i?kt(o,d)&&ut(r,"set",s,o):ut(r,"add",s,o),this},delete(s){const o=Y(this),{has:r,get:l}=wn(o);let a=r.call(o,s);a||(s=Y(s),a=r.call(o,s)),l&&l.call(o,s);const i=o.delete(s);return a&&ut(o,"delete",s,void 0),i},clear(){const s=Y(this),o=s.size!==0,r=s.clear();return o&&ut(s,"clear",void 0,void 0),r}}),["keys","values","entries",Symbol.iterator].forEach(s=>{n[s]=Fl(s,e,t)}),n}function Fs(e,t){const n=Ml(e,t);return(s,o,r)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?s:Reflect.get(Z(n,o)&&o in s?n:s,o,r)}const Pl={get:Fs(!1,!1)},Tl={get:Fs(!1,!0)},Il={get:Fs(!0,!1)},lr=new WeakMap,ar=new WeakMap,ir=new WeakMap,Rl=new WeakMap;function Vl(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Ll(e){return e.__v_skip||!Object.isExtensible(e)?0:Vl(fl(e))}function Ot(e){return Ct(e)?e:Ms(e,!1,jl,Pl,lr)}function cr(e){return Ms(e,!1,Nl,Tl,ar)}function ur(e){return Ms(e,!0,Ol,Il,ir)}function Ms(e,t,n,s,o){if(!ce(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=Ll(e);if(r===0)return e;const l=o.get(e);if(l)return l;const a=new Proxy(e,r===2?s:n);return o.set(e,a),a}function Ut(e){return Ct(e)?Ut(e.__v_raw):!!(e&&e.__v_isReactive)}function Ct(e){return!!(e&&e.__v_isReadonly)}function Le(e){return!!(e&&e.__v_isShallow)}function Ps(e){return e?!!e.__v_raw:!1}function Y(e){const t=e&&e.__v_raw;return t?Y(t):e}function Ul(e){return!Z(e,"__v_skip")&&Object.isExtensible(e)&&Qo(e,"__v_skip",!0),e}const be=e=>ce(e)?Ot(e):e,$n=e=>ce(e)?ur(e):e;function ke(e){return e?e.__v_isRef===!0:!1}function K(e){return dr(e,!1)}function Bl(e){return dr(e,!0)}function dr(e,t){return ke(e)?e:new ql(e,t)}class ql{constructor(t,n){this.dep=new Ns,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:Y(t),this._value=n?t:be(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Le(t)||Ct(t);t=s?t:Y(t),kt(t,n)&&(this._rawValue=t,this._value=s?t:be(t),this.dep.trigger())}}function D(e){return ke(e)?e.value:e}const zl={get:(e,t,n)=>t==="__v_raw"?e:D(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const o=e[t];return ke(o)&&!ke(n)?(o.value=n,!0):Reflect.set(e,t,n,s)}};function fr(e){return Ut(e)?e:new Proxy(e,zl)}class Ql{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Ns(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=cn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&ae!==this)return Xo(this,!0),!0}get value(){const t=this.dep.track();return er(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Jl(e,t,n=!1){let s,o;return Q(e)?s=e:(s=e.get,o=e.set),new Ql(s,o,n)}const kn={},En=new WeakMap;let Et;function Wl(e,t=!1,n=Et){if(n){let s=En.get(n);s||En.set(n,s=[]),s.push(e)}}function Gl(e,t,n=re){const{immediate:s,deep:o,once:r,scheduler:l,augmentJob:a,call:i}=n,d=T=>o?T:Le(T)||o===!1||o===0?dt(T,1):dt(T);let c,f,h,m,A=!1,C=!1;if(ke(e)?(f=()=>e.value,A=Le(e)):Ut(e)?(f=()=>d(e),A=!0):B(e)?(C=!0,A=e.some(T=>Ut(T)||Le(T)),f=()=>e.map(T=>{if(ke(T))return T.value;if(Ut(T))return d(T);if(Q(T))return i?i(T,2):T()})):Q(e)?t?f=i?()=>i(e,2):e:f=()=>{if(h){ft();try{h()}finally{pt()}}const T=Et;Et=c;try{return i?i(e,3,[m]):e(m)}finally{Et=T}}:f=Ze,t&&o){const T=f,H=o===!0?1/0:o;f=()=>dt(T(),H)}const M=xl(),R=()=>{c.stop(),M&&M.active&&Ds(M.effects,c)};if(r&&t){const T=t;t=(...H)=>{T(...H),R()}}let P=C?new Array(e.length).fill(kn):kn;const I=T=>{if(!(!(c.flags&1)||!c.dirty&&!T))if(t){const H=c.run();if(o||A||(C?H.some((ge,le)=>kt(ge,P[le])):kt(H,P))){h&&h();const ge=Et;Et=c;try{const le=[H,P===kn?void 0:C&&P[0]===kn?[]:P,m];P=H,i?i(t,3,le):t(...le)}finally{Et=ge}}}else c.run()};return a&&a(I),c=new Ko(f),c.scheduler=l?()=>l(I,!1):I,m=T=>Wl(T,!1,c),h=c.onStop=()=>{const T=En.get(c);if(T){if(i)i(T,4);else for(const H of T)H();En.delete(c)}},t?s?I(!0):P=c.run():l?l(I.bind(null,!0),!0):c.run(),R.pause=c.pause.bind(c),R.resume=c.resume.bind(c),R.stop=R,R}function dt(e,t=1/0,n){if(t<=0||!ce(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ke(e))dt(e.value,t,n);else if(B(e))for(let s=0;s<e.length;s++)dt(e[s],t,n);else if(Uo(e)||Lt(e))e.forEach(s=>{dt(s,t,n)});else if(zo(e)){for(const s in e)dt(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&dt(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.15
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function yn(e,t,n,s){try{return s?e(...s):e()}catch(o){Bn(o,t,n)}}function st(e,t,n,s){if(Q(e)){const o=yn(e,t,n,s);return o&&Bo(o)&&o.catch(r=>{Bn(r,t,n)}),o}if(B(e)){const o=[];for(let r=0;r<e.length;r++)o.push(st(e[r],t,n,s));return o}}function Bn(e,t,n,s=!0){const o=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:l}=t&&t.appContext.config||re;if(t){let a=t.parent;const i=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const c=a.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,i,d)===!1)return}a=a.parent}if(r){ft(),yn(r,null,10,[e,i,d]),pt();return}}Kl(e,n,o,s,l)}function Kl(e,t,n,s=!0,o=!1){if(o)throw e;console.error(e)}const Ae=[];let Xe=-1;const Bt=[];let _t=null,Pt=0;const pr=Promise.resolve();let jn=null;function Ts(e){const t=jn||pr;return e?t.then(this?e.bind(this):e):t}function Hl(e){let t=Xe+1,n=Ae.length;for(;t<n;){const s=t+n>>>1,o=Ae[s],r=dn(o);r<e||r===e&&o.flags&2?t=s+1:n=s}return t}function Is(e){if(!(e.flags&1)){const t=dn(e),n=Ae[Ae.length-1];!n||!(e.flags&2)&&t>=dn(n)?Ae.push(e):Ae.splice(Hl(t),0,e),e.flags|=1,hr()}}function hr(){jn||(jn=pr.then(vr))}function Xl(e){B(e)?Bt.push(...e):_t&&e.id===-1?_t.splice(Pt+1,0,e):e.flags&1||(Bt.push(e),e.flags|=1),hr()}function Ks(e,t,n=Xe+1){for(;n<Ae.length;n++){const s=Ae[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Ae.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function mr(e){if(Bt.length){const t=[...new Set(Bt)].sort((n,s)=>dn(n)-dn(s));if(Bt.length=0,_t){_t.push(...t);return}for(_t=t,Pt=0;Pt<_t.length;Pt++){const n=_t[Pt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}_t=null,Pt=0}}const dn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function vr(e){try{for(Xe=0;Xe<Ae.length;Xe++){const t=Ae[Xe];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),yn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Xe<Ae.length;Xe++){const t=Ae[Xe];t&&(t.flags&=-2)}Xe=-1,Ae.length=0,mr(),jn=null,(Ae.length||Bt.length)&&vr()}}let _e=null,gr=null;function On(e){const t=_e;return _e=e,gr=e&&e.type.__scopeId||null,t}function ee(e,t=_e,n){if(!t||e._n)return e;const s=(...o)=>{s._d&&lo(-1);const r=On(t);let l;try{l=e(...o)}finally{On(r),s._d&&lo(1)}return l};return s._n=!0,s._c=!0,s._d=!0,s}function et(e,t){if(_e===null)return e;const n=Jn(_e),s=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[r,l,a,i=re]=t[o];r&&(Q(r)&&(r={mounted:r,updated:r}),r.deep&&dt(l),s.push({dir:r,instance:n,value:l,oldValue:void 0,arg:a,modifiers:i}))}return e}function Dt(e,t,n,s){const o=e.dirs,r=t&&t.dirs;for(let l=0;l<o.length;l++){const a=o[l];r&&(a.oldValue=r[l].value);let i=a.dir[s];i&&(ft(),st(i,n,8,[e.el,a,e,t]),pt())}}const Yl=Symbol("_vte"),Zl=e=>e.__isTeleport;function Rs(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Rs(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function yr(e,t){return Q(e)?Ce({name:e.name},t,{setup:e}):e}function br(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Nn(e,t,n,s,o=!1){if(B(e)){e.forEach((A,C)=>Nn(A,t&&(B(t)?t[C]:t),n,s,o));return}if(qt(s)&&!o){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&Nn(e,t,n,s.component.subTree);return}const r=s.shapeFlag&4?Jn(s.component):s.el,l=o?null:r,{i:a,r:i}=e,d=t&&t.r,c=a.refs===re?a.refs={}:a.refs,f=a.setupState,h=Y(f),m=f===re?()=>!1:A=>Z(h,A);if(d!=null&&d!==i&&(ve(d)?(c[d]=null,m(d)&&(f[d]=null)):ke(d)&&(d.value=null)),Q(i))yn(i,a,12,[l,c]);else{const A=ve(i),C=ke(i);if(A||C){const M=()=>{if(e.f){const R=A?m(i)?f[i]:c[i]:i.value;o?B(R)&&Ds(R,r):B(R)?R.includes(r)||R.push(r):A?(c[i]=[r],m(i)&&(f[i]=c[i])):(i.value=[r],e.k&&(c[e.k]=i.value))}else A?(c[i]=l,m(i)&&(f[i]=l)):C&&(i.value=l,e.k&&(c[e.k]=l))};l?(M.id=-1,Fe(M,n)):M()}}}Vn().requestIdleCallback;Vn().cancelIdleCallback;const qt=e=>!!e.type.__asyncLoader,_r=e=>e.type.__isKeepAlive;function ea(e,t){wr(e,"a",t)}function ta(e,t){wr(e,"da",t)}function wr(e,t,n=xe){const s=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(qn(t,s,n),n){let o=n.parent;for(;o&&o.parent;)_r(o.parent.vnode)&&na(s,t,n,o),o=o.parent}}function na(e,t,n,s){const o=qn(t,e,s,!0);kr(()=>{Ds(s[t],o)},n)}function qn(e,t,n=xe,s=!1){if(n){const o=n[e]||(n[e]=[]),r=t.__weh||(t.__weh=(...l)=>{ft();const a=bn(n),i=st(t,n,e,l);return a(),pt(),i});return s?o.unshift(r):o.push(r),r}}const gt=e=>(t,n=xe)=>{(!hn||e==="sp")&&qn(e,(...s)=>t(...s),n)},sa=gt("bm"),xr=gt("m"),oa=gt("bu"),ra=gt("u"),la=gt("bum"),kr=gt("um"),aa=gt("sp"),ia=gt("rtg"),ca=gt("rtc");function ua(e,t=xe){qn("ec",e,t)}const da="components";function Hs(e,t){return pa(da,e,!0,t)||e}const fa=Symbol.for("v-ndc");function pa(e,t,n=!0,s=!1){const o=_e||xe;if(o){const r=o.type;{const a=Za(r,!1);if(a&&(a===t||a===Be(t)||a===Rn(Be(t))))return r}const l=Xs(o[e]||r[e],t)||Xs(o.appContext[e],t);return!l&&s?r:l}}function Xs(e,t){return e&&(e[t]||e[Be(t)]||e[Rn(Be(t))])}function Me(e,t,n,s){let o;const r=n,l=B(e);if(l||ve(e)){const a=l&&Ut(e);let i=!1,d=!1;a&&(i=!Le(e),d=Ct(e),e=Un(e)),o=new Array(e.length);for(let c=0,f=e.length;c<f;c++)o[c]=t(i?d?$n(be(e[c])):be(e[c]):e[c],c,void 0,r)}else if(typeof e=="number"){o=new Array(e);for(let a=0;a<e;a++)o[a]=t(a+1,a,void 0,r)}else if(ce(e))if(e[Symbol.iterator])o=Array.from(e,(a,i)=>t(a,i,void 0,r));else{const a=Object.keys(e);o=new Array(a.length);for(let i=0,d=a.length;i<d;i++){const c=a[i];o[i]=t(e[c],c,i,r)}}else o=[];return o}function Rt(e,t,n={},s,o){if(_e.ce||_e.parent&&qt(_e.parent)&&_e.parent.ce)return t!=="default"&&(n.name=t),x(),de(ie,null,[z("slot",n,s&&s())],64);let r=e[t];r&&r._c&&(r._d=!1),x();const l=r&&Cr(r(n)),a=n.key||l&&l.key,i=de(ie,{key:(a&&!vt(a)?a:`_${t}`)+(!l&&s?"_fb":"")},l||(s?s():[]),l&&e._===1?64:-2);return r&&r._c&&(r._d=!0),i}function Cr(e){return e.some(t=>pn(t)?!(t.type===ht||t.type===ie&&!Cr(t.children)):!0)?e:null}const vs=e=>e?zr(e)?Jn(e):vs(e.parent):null,on=Ce(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>vs(e.parent),$root:e=>vs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Ar(e),$forceUpdate:e=>e.f||(e.f=()=>{Is(e.update)}),$nextTick:e=>e.n||(e.n=Ts.bind(e.proxy)),$watch:e=>Ma.bind(e)}),ts=(e,t)=>e!==re&&!e.__isScriptSetup&&Z(e,t),ha={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:o,props:r,accessCache:l,type:a,appContext:i}=e;let d;if(t[0]!=="$"){const m=l[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return o[t];case 4:return n[t];case 3:return r[t]}else{if(ts(s,t))return l[t]=1,s[t];if(o!==re&&Z(o,t))return l[t]=2,o[t];if((d=e.propsOptions[0])&&Z(d,t))return l[t]=3,r[t];if(n!==re&&Z(n,t))return l[t]=4,n[t];gs&&(l[t]=0)}}const c=on[t];let f,h;if(c)return t==="$attrs"&&we(e.attrs,"get",""),c(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(n!==re&&Z(n,t))return l[t]=4,n[t];if(h=i.config.globalProperties,Z(h,t))return h[t]},set({_:e},t,n){const{data:s,setupState:o,ctx:r}=e;return ts(o,t)?(o[t]=n,!0):s!==re&&Z(s,t)?(s[t]=n,!0):Z(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:o,propsOptions:r}},l){let a;return!!n[l]||e!==re&&Z(e,l)||ts(t,l)||(a=r[0])&&Z(a,l)||Z(s,l)||Z(on,l)||Z(o.config.globalProperties,l)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Z(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Ys(e){return B(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let gs=!0;function ma(e){const t=Ar(e),n=e.proxy,s=e.ctx;gs=!1,t.beforeCreate&&Zs(t.beforeCreate,e,"bc");const{data:o,computed:r,methods:l,watch:a,provide:i,inject:d,created:c,beforeMount:f,mounted:h,beforeUpdate:m,updated:A,activated:C,deactivated:M,beforeDestroy:R,beforeUnmount:P,destroyed:I,unmounted:T,render:H,renderTracked:ge,renderTriggered:le,errorCaptured:je,serverPrefetch:Qe,expose:Te,inheritAttrs:Je,components:Ie,directives:ye,filters:At}=t;if(d&&va(d,s,null),l)for(const te in l){const W=l[te];Q(W)&&(s[te]=W.bind(n))}if(o){const te=o.call(n,n);ce(te)&&(e.data=Ot(te))}if(gs=!0,r)for(const te in r){const W=r[te],fe=Q(W)?W.bind(n,n):Q(W.get)?W.get.bind(n,n):Ze,Kt=!Q(W)&&Q(W.set)?W.set.bind(n):Ze,rt=se({get:fe,set:Kt});Object.defineProperty(s,te,{enumerable:!0,configurable:!0,get:()=>rt.value,set:lt=>rt.value=lt})}if(a)for(const te in a)Sr(a[te],s,n,te);if(i){const te=Q(i)?i.call(n):i;Reflect.ownKeys(te).forEach(W=>{Qt(W,te[W])})}c&&Zs(c,e,"c");function he(te,W){B(W)?W.forEach(fe=>te(fe.bind(n))):W&&te(W.bind(n))}if(he(sa,f),he(xr,h),he(oa,m),he(ra,A),he(ea,C),he(ta,M),he(ua,je),he(ca,ge),he(ia,le),he(la,P),he(kr,T),he(aa,Qe),B(Te))if(Te.length){const te=e.exposed||(e.exposed={});Te.forEach(W=>{Object.defineProperty(te,W,{get:()=>n[W],set:fe=>n[W]=fe})})}else e.exposed||(e.exposed={});H&&e.render===Ze&&(e.render=H),Je!=null&&(e.inheritAttrs=Je),Ie&&(e.components=Ie),ye&&(e.directives=ye),Qe&&br(e)}function va(e,t,n=Ze){B(e)&&(e=ys(e));for(const s in e){const o=e[s];let r;ce(o)?"default"in o?r=Ue(o.from||s,o.default,!0):r=Ue(o.from||s):r=Ue(o),ke(r)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>r.value,set:l=>r.value=l}):t[s]=r}}function Zs(e,t,n){st(B(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Sr(e,t,n,s){let o=s.includes(".")?Vr(n,s):()=>n[s];if(ve(e)){const r=t[e];Q(r)&&tt(o,r)}else if(Q(e))tt(o,e.bind(n));else if(ce(e))if(B(e))e.forEach(r=>Sr(r,t,n,s));else{const r=Q(e.handler)?e.handler.bind(n):t[e.handler];Q(r)&&tt(o,r,e)}}function Ar(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:o,optionsCache:r,config:{optionMergeStrategies:l}}=e.appContext,a=r.get(t);let i;return a?i=a:!o.length&&!n&&!s?i=t:(i={},o.length&&o.forEach(d=>Fn(i,d,l,!0)),Fn(i,t,l)),ce(t)&&r.set(t,i),i}function Fn(e,t,n,s=!1){const{mixins:o,extends:r}=t;r&&Fn(e,r,n,!0),o&&o.forEach(l=>Fn(e,l,n,!0));for(const l in t)if(!(s&&l==="expose")){const a=ga[l]||n&&n[l];e[l]=a?a(e[l],t[l]):t[l]}return e}const ga={data:eo,props:to,emits:to,methods:en,computed:en,beforeCreate:Se,created:Se,beforeMount:Se,mounted:Se,beforeUpdate:Se,updated:Se,beforeDestroy:Se,beforeUnmount:Se,destroyed:Se,unmounted:Se,activated:Se,deactivated:Se,errorCaptured:Se,serverPrefetch:Se,components:en,directives:en,watch:ba,provide:eo,inject:ya};function eo(e,t){return t?e?function(){return Ce(Q(e)?e.call(this,this):e,Q(t)?t.call(this,this):t)}:t:e}function ya(e,t){return en(ys(e),ys(t))}function ys(e){if(B(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Se(e,t){return e?[...new Set([].concat(e,t))]:t}function en(e,t){return e?Ce(Object.create(null),e,t):t}function to(e,t){return e?B(e)&&B(t)?[...new Set([...e,...t])]:Ce(Object.create(null),Ys(e),Ys(t??{})):t}function ba(e,t){if(!e)return t;if(!t)return e;const n=Ce(Object.create(null),e);for(const s in t)n[s]=Se(e[s],t[s]);return n}function Dr(){return{app:null,config:{isNativeTag:ul,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let _a=0;function wa(e,t){return function(n,s=null){Q(n)||(n=Ce({},n)),s!=null&&!ce(s)&&(s=null);const o=Dr(),r=new WeakSet,l=[];let a=!1;const i=o.app={_uid:_a++,_component:n,_props:s,_container:null,_context:o,_instance:null,version:ti,get config(){return o.config},set config(d){},use(d,...c){return r.has(d)||(d&&Q(d.install)?(r.add(d),d.install(i,...c)):Q(d)&&(r.add(d),d(i,...c))),i},mixin(d){return o.mixins.includes(d)||o.mixins.push(d),i},component(d,c){return c?(o.components[d]=c,i):o.components[d]},directive(d,c){return c?(o.directives[d]=c,i):o.directives[d]},mount(d,c,f){if(!a){const h=i._ceVNode||z(n,s);return h.appContext=o,f===!0?f="svg":f===!1&&(f=void 0),e(h,d,f),a=!0,i._container=d,d.__vue_app__=i,Jn(h.component)}},onUnmount(d){l.push(d)},unmount(){a&&(st(l,i._instance,16),e(null,i._container),delete i._container.__vue_app__)},provide(d,c){return o.provides[d]=c,i},runWithContext(d){const c=zt;zt=i;try{return d()}finally{zt=c}}};return i}}let zt=null;function Qt(e,t){if(xe){let n=xe.provides;const s=xe.parent&&xe.parent.provides;s===n&&(n=xe.provides=Object.create(s)),n[e]=t}}function Ue(e,t,n=!1){const s=xe||_e;if(s||zt){let o=zt?zt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&Q(t)?t.call(s&&s.proxy):t}}const $r={},Er=()=>Object.create($r),jr=e=>Object.getPrototypeOf(e)===$r;function xa(e,t,n,s=!1){const o={},r=Er();e.propsDefaults=Object.create(null),Or(e,t,o,r);for(const l in e.propsOptions[0])l in o||(o[l]=void 0);n?e.props=s?o:cr(o):e.type.props?e.props=o:e.props=r,e.attrs=r}function ka(e,t,n,s){const{props:o,attrs:r,vnode:{patchFlag:l}}=e,a=Y(o),[i]=e.propsOptions;let d=!1;if((s||l>0)&&!(l&16)){if(l&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let h=c[f];if(zn(e.emitsOptions,h))continue;const m=t[h];if(i)if(Z(r,h))m!==r[h]&&(r[h]=m,d=!0);else{const A=Be(h);o[A]=bs(i,a,A,m,e,!1)}else m!==r[h]&&(r[h]=m,d=!0)}}}else{Or(e,t,o,r)&&(d=!0);let c;for(const f in a)(!t||!Z(t,f)&&((c=St(f))===f||!Z(t,c)))&&(i?n&&(n[f]!==void 0||n[c]!==void 0)&&(o[f]=bs(i,a,f,void 0,e,!0)):delete o[f]);if(r!==a)for(const f in r)(!t||!Z(t,f))&&(delete r[f],d=!0)}d&&ut(e.attrs,"set","")}function Or(e,t,n,s){const[o,r]=e.propsOptions;let l=!1,a;if(t)for(let i in t){if(tn(i))continue;const d=t[i];let c;o&&Z(o,c=Be(i))?!r||!r.includes(c)?n[c]=d:(a||(a={}))[c]=d:zn(e.emitsOptions,i)||(!(i in s)||d!==s[i])&&(s[i]=d,l=!0)}if(r){const i=Y(n),d=a||re;for(let c=0;c<r.length;c++){const f=r[c];n[f]=bs(o,i,f,d[f],e,!Z(d,f))}}return l}function bs(e,t,n,s,o,r){const l=e[n];if(l!=null){const a=Z(l,"default");if(a&&s===void 0){const i=l.default;if(l.type!==Function&&!l.skipFactory&&Q(i)){const{propsDefaults:d}=o;if(n in d)s=d[n];else{const c=bn(o);s=d[n]=i.call(null,t),c()}}else s=i;o.ce&&o.ce._setProp(n,s)}l[0]&&(r&&!a?s=!1:l[1]&&(s===""||s===St(n))&&(s=!0))}return s}const Ca=new WeakMap;function Nr(e,t,n=!1){const s=n?Ca:t.propsCache,o=s.get(e);if(o)return o;const r=e.props,l={},a=[];let i=!1;if(!Q(e)){const c=f=>{i=!0;const[h,m]=Nr(f,t,!0);Ce(l,h),m&&a.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!r&&!i)return ce(e)&&s.set(e,Vt),Vt;if(B(r))for(let c=0;c<r.length;c++){const f=Be(r[c]);no(f)&&(l[f]=re)}else if(r)for(const c in r){const f=Be(c);if(no(f)){const h=r[c],m=l[f]=B(h)||Q(h)?{type:h}:Ce({},h),A=m.type;let C=!1,M=!0;if(B(A))for(let R=0;R<A.length;++R){const P=A[R],I=Q(P)&&P.name;if(I==="Boolean"){C=!0;break}else I==="String"&&(M=!1)}else C=Q(A)&&A.name==="Boolean";m[0]=C,m[1]=M,(C||Z(m,"default"))&&a.push(f)}}const d=[l,a];return ce(e)&&s.set(e,d),d}function no(e){return e[0]!=="$"&&!tn(e)}const Vs=e=>e[0]==="_"||e==="$stable",Ls=e=>B(e)?e.map(Ye):[Ye(e)],Sa=(e,t,n)=>{if(t._n)return t;const s=ee((...o)=>Ls(t(...o)),n);return s._c=!1,s},Fr=(e,t,n)=>{const s=e._ctx;for(const o in e){if(Vs(o))continue;const r=e[o];if(Q(r))t[o]=Sa(o,r,s);else if(r!=null){const l=Ls(r);t[o]=()=>l}}},Mr=(e,t)=>{const n=Ls(t);e.slots.default=()=>n},Pr=(e,t,n)=>{for(const s in t)(n||!Vs(s))&&(e[s]=t[s])},Aa=(e,t,n)=>{const s=e.slots=Er();if(e.vnode.shapeFlag&32){const o=t._;o?(Pr(s,t,n),n&&Qo(s,"_",o,!0)):Fr(t,s)}else t&&Mr(e,t)},Da=(e,t,n)=>{const{vnode:s,slots:o}=e;let r=!0,l=re;if(s.shapeFlag&32){const a=t._;a?n&&a===1?r=!1:Pr(o,t,n):(r=!t.$stable,Fr(t,o)),l=t}else t&&(Mr(e,t),l={default:1});if(r)for(const a in o)!Vs(a)&&l[a]==null&&delete o[a]},Fe=Ua;function $a(e){return Ea(e)}function Ea(e,t){const n=Vn();n.__VUE__=!0;const{insert:s,remove:o,patchProp:r,createElement:l,createText:a,createComment:i,setText:d,setElementText:c,parentNode:f,nextSibling:h,setScopeId:m=Ze,insertStaticContent:A}=e,C=(u,p,v,y=null,b=null,w=null,S=void 0,E=null,$=!!p.dynamicChildren)=>{if(u===p)return;u&&!Yt(u,p)&&(y=_(u),Re(u,b,w,!0),u=null),p.patchFlag===-2&&($=!1,p.dynamicChildren=null);const{type:k,ref:V,shapeFlag:N}=p;switch(k){case Qn:M(u,p,v,y);break;case ht:R(u,p,v,y);break;case ss:u==null&&P(p,v,y,S);break;case ie:Ie(u,p,v,y,b,w,S,E,$);break;default:N&1?H(u,p,v,y,b,w,S,E,$):N&6?ye(u,p,v,y,b,w,S,E,$):(N&64||N&128)&&k.process(u,p,v,y,b,w,S,E,$,L)}V!=null&&b&&Nn(V,u&&u.ref,w,p||u,!p)},M=(u,p,v,y)=>{if(u==null)s(p.el=a(p.children),v,y);else{const b=p.el=u.el;p.children!==u.children&&d(b,p.children)}},R=(u,p,v,y)=>{u==null?s(p.el=i(p.children||""),v,y):p.el=u.el},P=(u,p,v,y)=>{[u.el,u.anchor]=A(u.children,p,v,y,u.el,u.anchor)},I=({el:u,anchor:p},v,y)=>{let b;for(;u&&u!==p;)b=h(u),s(u,v,y),u=b;s(p,v,y)},T=({el:u,anchor:p})=>{let v;for(;u&&u!==p;)v=h(u),o(u),u=v;o(p)},H=(u,p,v,y,b,w,S,E,$)=>{p.type==="svg"?S="svg":p.type==="math"&&(S="mathml"),u==null?ge(p,v,y,b,w,S,E,$):Qe(u,p,b,w,S,E,$)},ge=(u,p,v,y,b,w,S,E)=>{let $,k;const{props:V,shapeFlag:N,transition:U,dirs:q}=u;if($=u.el=l(u.type,w,V&&V.is,V),N&8?c($,u.children):N&16&&je(u.children,$,null,y,b,ns(u,w),S,E),q&&Dt(u,null,y,"created"),le($,u,u.scopeId,S,y),V){for(const ue in V)ue!=="value"&&!tn(ue)&&r($,ue,null,V[ue],w,y);"value"in V&&r($,"value",null,V.value,w),(k=V.onVnodeBeforeMount)&&He(k,y,u)}q&&Dt(u,null,y,"beforeMount");const G=ja(b,U);G&&U.beforeEnter($),s($,p,v),((k=V&&V.onVnodeMounted)||G||q)&&Fe(()=>{k&&He(k,y,u),G&&U.enter($),q&&Dt(u,null,y,"mounted")},b)},le=(u,p,v,y,b)=>{if(v&&m(u,v),y)for(let w=0;w<y.length;w++)m(u,y[w]);if(b){let w=b.subTree;if(p===w||Ur(w.type)&&(w.ssContent===p||w.ssFallback===p)){const S=b.vnode;le(u,S,S.scopeId,S.slotScopeIds,b.parent)}}},je=(u,p,v,y,b,w,S,E,$=0)=>{for(let k=$;k<u.length;k++){const V=u[k]=E?wt(u[k]):Ye(u[k]);C(null,V,p,v,y,b,w,S,E)}},Qe=(u,p,v,y,b,w,S)=>{const E=p.el=u.el;let{patchFlag:$,dynamicChildren:k,dirs:V}=p;$|=u.patchFlag&16;const N=u.props||re,U=p.props||re;let q;if(v&&$t(v,!1),(q=U.onVnodeBeforeUpdate)&&He(q,v,p,u),V&&Dt(p,u,v,"beforeUpdate"),v&&$t(v,!0),(N.innerHTML&&U.innerHTML==null||N.textContent&&U.textContent==null)&&c(E,""),k?Te(u.dynamicChildren,k,E,v,y,ns(p,b),w):S||fe(u,p,E,null,v,y,ns(p,b),w,!1),$>0){if($&16)Je(E,N,U,v,b);else if($&2&&N.class!==U.class&&r(E,"class",null,U.class,b),$&4&&r(E,"style",N.style,U.style,b),$&8){const G=p.dynamicProps;for(let ue=0;ue<G.length;ue++){const ne=G[ue],Oe=N[ne],De=U[ne];(De!==Oe||ne==="value")&&r(E,ne,Oe,De,b,v)}}$&1&&u.children!==p.children&&c(E,p.children)}else!S&&k==null&&Je(E,N,U,v,b);((q=U.onVnodeUpdated)||V)&&Fe(()=>{q&&He(q,v,p,u),V&&Dt(p,u,v,"updated")},y)},Te=(u,p,v,y,b,w,S)=>{for(let E=0;E<p.length;E++){const $=u[E],k=p[E],V=$.el&&($.type===ie||!Yt($,k)||$.shapeFlag&198)?f($.el):v;C($,k,V,null,y,b,w,S,!0)}},Je=(u,p,v,y,b)=>{if(p!==v){if(p!==re)for(const w in p)!tn(w)&&!(w in v)&&r(u,w,p[w],null,b,y);for(const w in v){if(tn(w))continue;const S=v[w],E=p[w];S!==E&&w!=="value"&&r(u,w,E,S,b,y)}"value"in v&&r(u,"value",p.value,v.value,b)}},Ie=(u,p,v,y,b,w,S,E,$)=>{const k=p.el=u?u.el:a(""),V=p.anchor=u?u.anchor:a("");let{patchFlag:N,dynamicChildren:U,slotScopeIds:q}=p;q&&(E=E?E.concat(q):q),u==null?(s(k,v,y),s(V,v,y),je(p.children||[],v,V,b,w,S,E,$)):N>0&&N&64&&U&&u.dynamicChildren?(Te(u.dynamicChildren,U,v,b,w,S,E),(p.key!=null||b&&p===b.subTree)&&Tr(u,p,!0)):fe(u,p,v,V,b,w,S,E,$)},ye=(u,p,v,y,b,w,S,E,$)=>{p.slotScopeIds=E,u==null?p.shapeFlag&512?b.ctx.activate(p,v,y,S,$):At(p,v,y,b,w,S,$):he(u,p,$)},At=(u,p,v,y,b,w,S)=>{const E=u.component=Ga(u,y,b);if(_r(u)&&(E.ctx.renderer=L),Ka(E,!1,S),E.asyncDep){if(b&&b.registerDep(E,te,S),!u.el){const $=E.subTree=z(ht);R(null,$,p,v)}}else te(E,u,p,v,b,w,S)},he=(u,p,v)=>{const y=p.component=u.component;if(Va(u,p,v))if(y.asyncDep&&!y.asyncResolved){W(y,p,v);return}else y.next=p,y.update();else p.el=u.el,y.vnode=p},te=(u,p,v,y,b,w,S)=>{const E=()=>{if(u.isMounted){let{next:N,bu:U,u:q,parent:G,vnode:ue}=u;{const Ge=Ir(u);if(Ge){N&&(N.el=ue.el,W(u,N,S)),Ge.asyncDep.then(()=>{u.isUnmounted||E()});return}}let ne=N,Oe;$t(u,!1),N?(N.el=ue.el,W(u,N,S)):N=ue,U&&Sn(U),(Oe=N.props&&N.props.onVnodeBeforeUpdate)&&He(Oe,G,N,ue),$t(u,!0);const De=oo(u),We=u.subTree;u.subTree=De,C(We,De,f(We.el),_(We),u,b,w),N.el=De.el,ne===null&&La(u,De.el),q&&Fe(q,b),(Oe=N.props&&N.props.onVnodeUpdated)&&Fe(()=>He(Oe,G,N,ue),b)}else{let N;const{el:U,props:q}=p,{bm:G,m:ue,parent:ne,root:Oe,type:De}=u,We=qt(p);$t(u,!1),G&&Sn(G),!We&&(N=q&&q.onVnodeBeforeMount)&&He(N,ne,p),$t(u,!0);{Oe.ce&&Oe.ce._injectChildStyle(De);const Ge=u.subTree=oo(u);C(null,Ge,v,y,u,b,w),p.el=Ge.el}if(ue&&Fe(ue,b),!We&&(N=q&&q.onVnodeMounted)){const Ge=p;Fe(()=>He(N,ne,Ge),b)}(p.shapeFlag&256||ne&&qt(ne.vnode)&&ne.vnode.shapeFlag&256)&&u.a&&Fe(u.a,b),u.isMounted=!0,p=v=y=null}};u.scope.on();const $=u.effect=new Ko(E);u.scope.off();const k=u.update=$.run.bind($),V=u.job=$.runIfDirty.bind($);V.i=u,V.id=u.uid,$.scheduler=()=>Is(V),$t(u,!0),k()},W=(u,p,v)=>{p.component=u;const y=u.vnode.props;u.vnode=p,u.next=null,ka(u,p.props,y,v),Da(u,p.children,v),ft(),Ks(u),pt()},fe=(u,p,v,y,b,w,S,E,$=!1)=>{const k=u&&u.children,V=u?u.shapeFlag:0,N=p.children,{patchFlag:U,shapeFlag:q}=p;if(U>0){if(U&128){rt(k,N,v,y,b,w,S,E,$);return}else if(U&256){Kt(k,N,v,y,b,w,S,E,$);return}}q&8?(V&16&&Ve(k,b,w),N!==k&&c(v,N)):V&16?q&16?rt(k,N,v,y,b,w,S,E,$):Ve(k,b,w,!0):(V&8&&c(v,""),q&16&&je(N,v,y,b,w,S,E,$))},Kt=(u,p,v,y,b,w,S,E,$)=>{u=u||Vt,p=p||Vt;const k=u.length,V=p.length,N=Math.min(k,V);let U;for(U=0;U<N;U++){const q=p[U]=$?wt(p[U]):Ye(p[U]);C(u[U],q,v,null,b,w,S,E,$)}k>V?Ve(u,b,w,!0,!1,N):je(p,v,y,b,w,S,E,$,N)},rt=(u,p,v,y,b,w,S,E,$)=>{let k=0;const V=p.length;let N=u.length-1,U=V-1;for(;k<=N&&k<=U;){const q=u[k],G=p[k]=$?wt(p[k]):Ye(p[k]);if(Yt(q,G))C(q,G,v,null,b,w,S,E,$);else break;k++}for(;k<=N&&k<=U;){const q=u[N],G=p[U]=$?wt(p[U]):Ye(p[U]);if(Yt(q,G))C(q,G,v,null,b,w,S,E,$);else break;N--,U--}if(k>N){if(k<=U){const q=U+1,G=q<V?p[q].el:y;for(;k<=U;)C(null,p[k]=$?wt(p[k]):Ye(p[k]),v,G,b,w,S,E,$),k++}}else if(k>U)for(;k<=N;)Re(u[k],b,w,!0),k++;else{const q=k,G=k,ue=new Map;for(k=G;k<=U;k++){const Ne=p[k]=$?wt(p[k]):Ye(p[k]);Ne.key!=null&&ue.set(Ne.key,k)}let ne,Oe=0;const De=U-G+1;let We=!1,Ge=0;const Ht=new Array(De);for(k=0;k<De;k++)Ht[k]=0;for(k=q;k<=N;k++){const Ne=u[k];if(Oe>=De){Re(Ne,b,w,!0);continue}let Ke;if(Ne.key!=null)Ke=ue.get(Ne.key);else for(ne=G;ne<=U;ne++)if(Ht[ne-G]===0&&Yt(Ne,p[ne])){Ke=ne;break}Ke===void 0?Re(Ne,b,w,!0):(Ht[Ke-G]=k+1,Ke>=Ge?Ge=Ke:We=!0,C(Ne,p[Ke],v,null,b,w,S,E,$),Oe++)}const zs=We?Oa(Ht):Vt;for(ne=zs.length-1,k=De-1;k>=0;k--){const Ne=G+k,Ke=p[Ne],Qs=Ne+1<V?p[Ne+1].el:y;Ht[k]===0?C(null,Ke,v,Qs,b,w,S,E,$):We&&(ne<0||k!==zs[ne]?lt(Ke,v,Qs,2):ne--)}}},lt=(u,p,v,y,b=null)=>{const{el:w,type:S,transition:E,children:$,shapeFlag:k}=u;if(k&6){lt(u.component.subTree,p,v,y);return}if(k&128){u.suspense.move(p,v,y);return}if(k&64){S.move(u,p,v,L);return}if(S===ie){s(w,p,v);for(let V=0;V<$.length;V++)lt($[V],p,v,y);s(u.anchor,p,v);return}if(S===ss){I(u,p,v);return}if(y!==2&&k&1&&E)if(y===0)E.beforeEnter(w),s(w,p,v),Fe(()=>E.enter(w),b);else{const{leave:V,delayLeave:N,afterLeave:U}=E,q=()=>{u.ctx.isUnmounted?o(w):s(w,p,v)},G=()=>{V(w,()=>{q(),U&&U()})};N?N(w,q,G):G()}else s(w,p,v)},Re=(u,p,v,y=!1,b=!1)=>{const{type:w,props:S,ref:E,children:$,dynamicChildren:k,shapeFlag:V,patchFlag:N,dirs:U,cacheIndex:q}=u;if(N===-2&&(b=!1),E!=null&&(ft(),Nn(E,null,v,u,!0),pt()),q!=null&&(p.renderCache[q]=void 0),V&256){p.ctx.deactivate(u);return}const G=V&1&&U,ue=!qt(u);let ne;if(ue&&(ne=S&&S.onVnodeBeforeUnmount)&&He(ne,p,u),V&6)_n(u.component,v,y);else{if(V&128){u.suspense.unmount(v,y);return}G&&Dt(u,null,p,"beforeUnmount"),V&64?u.type.remove(u,p,v,L,y):k&&!k.hasOnce&&(w!==ie||N>0&&N&64)?Ve(k,p,v,!1,!0):(w===ie&&N&384||!b&&V&16)&&Ve($,p,v),y&&Nt(u)}(ue&&(ne=S&&S.onVnodeUnmounted)||G)&&Fe(()=>{ne&&He(ne,p,u),G&&Dt(u,null,p,"unmounted")},v)},Nt=u=>{const{type:p,el:v,anchor:y,transition:b}=u;if(p===ie){Ft(v,y);return}if(p===ss){T(u);return}const w=()=>{o(v),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(u.shapeFlag&1&&b&&!b.persisted){const{leave:S,delayLeave:E}=b,$=()=>S(v,w);E?E(u.el,w,$):$()}else w()},Ft=(u,p)=>{let v;for(;u!==p;)v=h(u),o(u),u=v;o(p)},_n=(u,p,v)=>{const{bum:y,scope:b,job:w,subTree:S,um:E,m:$,a:k,parent:V,slots:{__:N}}=u;so($),so(k),y&&Sn(y),V&&B(N)&&N.forEach(U=>{V.renderCache[U]=void 0}),b.stop(),w&&(w.flags|=8,Re(S,u,p,v)),E&&Fe(E,p),Fe(()=>{u.isUnmounted=!0},p),p&&p.pendingBranch&&!p.isUnmounted&&u.asyncDep&&!u.asyncResolved&&u.suspenseId===p.pendingId&&(p.deps--,p.deps===0&&p.resolve())},Ve=(u,p,v,y=!1,b=!1,w=0)=>{for(let S=w;S<u.length;S++)Re(u[S],p,v,y,b)},_=u=>{if(u.shapeFlag&6)return _(u.component.subTree);if(u.shapeFlag&128)return u.suspense.next();const p=h(u.anchor||u.el),v=p&&p[Yl];return v?h(v):p};let F=!1;const j=(u,p,v)=>{u==null?p._vnode&&Re(p._vnode,null,null,!0):C(p._vnode||null,u,p,null,null,null,v),p._vnode=u,F||(F=!0,Ks(),mr(),F=!1)},L={p:C,um:Re,m:lt,r:Nt,mt:At,mc:je,pc:fe,pbc:Te,n:_,o:e};return{render:j,hydrate:void 0,createApp:wa(j)}}function ns({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function $t({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function ja(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Tr(e,t,n=!1){const s=e.children,o=t.children;if(B(s)&&B(o))for(let r=0;r<s.length;r++){const l=s[r];let a=o[r];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=o[r]=wt(o[r]),a.el=l.el),!n&&a.patchFlag!==-2&&Tr(l,a)),a.type===Qn&&(a.el=l.el),a.type===ht&&!a.el&&(a.el=l.el)}}function Oa(e){const t=e.slice(),n=[0];let s,o,r,l,a;const i=e.length;for(s=0;s<i;s++){const d=e[s];if(d!==0){if(o=n[n.length-1],e[o]<d){t[s]=o,n.push(s);continue}for(r=0,l=n.length-1;r<l;)a=r+l>>1,e[n[a]]<d?r=a+1:l=a;d<e[n[r]]&&(r>0&&(t[s]=n[r-1]),n[r]=s)}}for(r=n.length,l=n[r-1];r-- >0;)n[r]=l,l=t[l];return n}function Ir(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ir(t)}function so(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Na=Symbol.for("v-scx"),Fa=()=>Ue(Na);function tt(e,t,n){return Rr(e,t,n)}function Rr(e,t,n=re){const{immediate:s,deep:o,flush:r,once:l}=n,a=Ce({},n),i=t&&s||!t&&r!=="post";let d;if(hn){if(r==="sync"){const m=Fa();d=m.__watcherHandles||(m.__watcherHandles=[])}else if(!i){const m=()=>{};return m.stop=Ze,m.resume=Ze,m.pause=Ze,m}}const c=xe;a.call=(m,A,C)=>st(m,c,A,C);let f=!1;r==="post"?a.scheduler=m=>{Fe(m,c&&c.suspense)}:r!=="sync"&&(f=!0,a.scheduler=(m,A)=>{A?m():Is(m)}),a.augmentJob=m=>{t&&(m.flags|=4),f&&(m.flags|=2,c&&(m.id=c.uid,m.i=c))};const h=Gl(e,t,a);return hn&&(d?d.push(h):i&&h()),h}function Ma(e,t,n){const s=this.proxy,o=ve(e)?e.includes(".")?Vr(s,e):()=>s[e]:e.bind(s,s);let r;Q(t)?r=t:(r=t.handler,n=t);const l=bn(this),a=Rr(o,r.bind(s),n);return l(),a}function Vr(e,t){const n=t.split(".");return()=>{let s=e;for(let o=0;o<n.length&&s;o++)s=s[n[o]];return s}}const Pa=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Be(t)}Modifiers`]||e[`${St(t)}Modifiers`];function Ta(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||re;let o=n;const r=t.startsWith("update:"),l=r&&Pa(s,t.slice(7));l&&(l.trim&&(o=n.map(c=>ve(c)?c.trim():c)),l.number&&(o=n.map(ds)));let a,i=s[a=Hn(t)]||s[a=Hn(Be(t))];!i&&r&&(i=s[a=Hn(St(t))]),i&&st(i,e,6,o);const d=s[a+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,st(d,e,6,o)}}function Lr(e,t,n=!1){const s=t.emitsCache,o=s.get(e);if(o!==void 0)return o;const r=e.emits;let l={},a=!1;if(!Q(e)){const i=d=>{const c=Lr(d,t,!0);c&&(a=!0,Ce(l,c))};!n&&t.mixins.length&&t.mixins.forEach(i),e.extends&&i(e.extends),e.mixins&&e.mixins.forEach(i)}return!r&&!a?(ce(e)&&s.set(e,null),null):(B(r)?r.forEach(i=>l[i]=null):Ce(l,r),ce(e)&&s.set(e,l),l)}function zn(e,t){return!e||!Pn(t)?!1:(t=t.slice(2).replace(/Once$/,""),Z(e,t[0].toLowerCase()+t.slice(1))||Z(e,St(t))||Z(e,t))}function oo(e){const{type:t,vnode:n,proxy:s,withProxy:o,propsOptions:[r],slots:l,attrs:a,emit:i,render:d,renderCache:c,props:f,data:h,setupState:m,ctx:A,inheritAttrs:C}=e,M=On(e);let R,P;try{if(n.shapeFlag&4){const T=o||s,H=T;R=Ye(d.call(H,T,c,f,m,h,A)),P=a}else{const T=t;R=Ye(T.length>1?T(f,{attrs:a,slots:l,emit:i}):T(f,null)),P=t.props?a:Ia(a)}}catch(T){rn.length=0,Bn(T,e,1),R=z(ht)}let I=R;if(P&&C!==!1){const T=Object.keys(P),{shapeFlag:H}=I;T.length&&H&7&&(r&&T.some(As)&&(P=Ra(P,r)),I=Jt(I,P,!1,!0))}return n.dirs&&(I=Jt(I,null,!1,!0),I.dirs=I.dirs?I.dirs.concat(n.dirs):n.dirs),n.transition&&Rs(I,n.transition),R=I,On(M),R}const Ia=e=>{let t;for(const n in e)(n==="class"||n==="style"||Pn(n))&&((t||(t={}))[n]=e[n]);return t},Ra=(e,t)=>{const n={};for(const s in e)(!As(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Va(e,t,n){const{props:s,children:o,component:r}=e,{props:l,children:a,patchFlag:i}=t,d=r.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&i>=0){if(i&1024)return!0;if(i&16)return s?ro(s,l,d):!!l;if(i&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const h=c[f];if(l[h]!==s[h]&&!zn(d,h))return!0}}}else return(o||a)&&(!a||!a.$stable)?!0:s===l?!1:s?l?ro(s,l,d):!0:!!l;return!1}function ro(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let o=0;o<s.length;o++){const r=s[o];if(t[r]!==e[r]&&!zn(n,r))return!0}return!1}function La({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Ur=e=>e.__isSuspense;function Ua(e,t){t&&t.pendingBranch?B(e)?t.effects.push(...e):t.effects.push(e):Xl(e)}const ie=Symbol.for("v-fgt"),Qn=Symbol.for("v-txt"),ht=Symbol.for("v-cmt"),ss=Symbol.for("v-stc"),rn=[];let Pe=null;function x(e=!1){rn.push(Pe=e?null:[])}function Ba(){rn.pop(),Pe=rn[rn.length-1]||null}let fn=1;function lo(e,t=!1){fn+=e,e<0&&Pe&&t&&(Pe.hasOnce=!0)}function Br(e){return e.dynamicChildren=fn>0?Pe||Vt:null,Ba(),fn>0&&Pe&&Pe.push(e),e}function O(e,t,n,s,o,r){return Br(g(e,t,n,s,o,r,!0))}function de(e,t,n,s,o){return Br(z(e,t,n,s,o,!0))}function pn(e){return e?e.__v_isVNode===!0:!1}function Yt(e,t){return e.type===t.type&&e.key===t.key}const qr=({key:e})=>e??null,An=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?ve(e)||ke(e)||Q(e)?{i:_e,r:e,k:t,f:!!n}:e:null);function g(e,t=null,n=null,s=0,o=null,r=e===ie?0:1,l=!1,a=!1){const i={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&qr(t),ref:t&&An(t),scopeId:gr,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:s,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:_e};return a?(Us(i,n),r&128&&e.normalize(i)):n&&(i.shapeFlag|=ve(n)?8:16),fn>0&&!l&&Pe&&(i.patchFlag>0||r&6)&&i.patchFlag!==32&&Pe.push(i),i}const z=qa;function qa(e,t=null,n=null,s=0,o=null,r=!1){if((!e||e===fa)&&(e=ht),pn(e)){const a=Jt(e,t,!0);return n&&Us(a,n),fn>0&&!r&&Pe&&(a.shapeFlag&6?Pe[Pe.indexOf(e)]=a:Pe.push(a)),a.patchFlag=-2,a}if(ei(e)&&(e=e.__vccOpts),t){t=za(t);let{class:a,style:i}=t;a&&!ve(a)&&(t.class=Ee(a)),ce(i)&&(Ps(i)&&!B(i)&&(i=Ce({},i)),t.style=Ln(i))}const l=ve(e)?1:Ur(e)?128:Zl(e)?64:ce(e)?4:Q(e)?2:0;return g(e,t,n,s,o,l,r,!0)}function za(e){return e?Ps(e)||jr(e)?Ce({},e):e:null}function Jt(e,t,n=!1,s=!1){const{props:o,ref:r,patchFlag:l,children:a,transition:i}=e,d=t?Qa(o||{},t):o,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&qr(d),ref:t&&t.ref?n&&r?B(r)?r.concat(An(t)):[r,An(t)]:An(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ie?l===-1?16:l|16:l,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:i,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Jt(e.ssContent),ssFallback:e.ssFallback&&Jt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return i&&s&&Rs(c,i.clone(c)),c}function me(e=" ",t=0){return z(Qn,null,e,t)}function oe(e="",t=!1){return t?(x(),de(ht,null,e)):z(ht,null,e)}function Ye(e){return e==null||typeof e=="boolean"?z(ht):B(e)?z(ie,null,e.slice()):pn(e)?wt(e):z(Qn,null,String(e))}function wt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Jt(e)}function Us(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(B(t))n=16;else if(typeof t=="object")if(s&65){const o=t.default;o&&(o._c&&(o._d=!1),Us(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!jr(t)?t._ctx=_e:o===3&&_e&&(_e.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Q(t)?(t={default:t,_ctx:_e},n=32):(t=String(t),s&64?(n=16,t=[me(t)]):n=8);e.children=t,e.shapeFlag|=n}function Qa(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const o in s)if(o==="class")t.class!==s.class&&(t.class=Ee([t.class,s.class]));else if(o==="style")t.style=Ln([t.style,s.style]);else if(Pn(o)){const r=t[o],l=s[o];l&&r!==l&&!(B(r)&&r.includes(l))&&(t[o]=r?[].concat(r,l):l)}else o!==""&&(t[o]=s[o])}return t}function He(e,t,n,s=null){st(e,t,7,[n,s])}const Ja=Dr();let Wa=0;function Ga(e,t,n){const s=e.type,o=(t?t.appContext:e.appContext)||Ja,r={uid:Wa++,vnode:e,type:s,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new wl(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Nr(s,o),emitsOptions:Lr(s,o),emit:null,emitted:null,propsDefaults:re,inheritAttrs:s.inheritAttrs,ctx:re,data:re,props:re,attrs:re,slots:re,refs:re,setupState:re,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=Ta.bind(null,r),e.ce&&e.ce(r),r}let xe=null,Mn,_s;{const e=Vn(),t=(n,s)=>{let o;return(o=e[n])||(o=e[n]=[]),o.push(s),r=>{o.length>1?o.forEach(l=>l(r)):o[0](r)}};Mn=t("__VUE_INSTANCE_SETTERS__",n=>xe=n),_s=t("__VUE_SSR_SETTERS__",n=>hn=n)}const bn=e=>{const t=xe;return Mn(e),e.scope.on(),()=>{e.scope.off(),Mn(t)}},ao=()=>{xe&&xe.scope.off(),Mn(null)};function zr(e){return e.vnode.shapeFlag&4}let hn=!1;function Ka(e,t=!1,n=!1){t&&_s(t);const{props:s,children:o}=e.vnode,r=zr(e);xa(e,s,r,t),Aa(e,o,n||t);const l=r?Ha(e,t):void 0;return t&&_s(!1),l}function Ha(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,ha);const{setup:s}=n;if(s){ft();const o=e.setupContext=s.length>1?Ya(e):null,r=bn(e),l=yn(s,e,0,[e.props,o]),a=Bo(l);if(pt(),r(),(a||e.sp)&&!qt(e)&&br(e),a){if(l.then(ao,ao),t)return l.then(i=>{io(e,i)}).catch(i=>{Bn(i,e,0)});e.asyncDep=l}else io(e,l)}else Qr(e)}function io(e,t,n){Q(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ce(t)&&(e.setupState=fr(t)),Qr(e)}function Qr(e,t,n){const s=e.type;e.render||(e.render=s.render||Ze);{const o=bn(e);ft();try{ma(e)}finally{pt(),o()}}}const Xa={get(e,t){return we(e,"get",""),e[t]}};function Ya(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Xa),slots:e.slots,emit:e.emit,expose:t}}function Jn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(fr(Ul(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in on)return on[n](e)},has(t,n){return n in t||n in on}})):e.proxy}function Za(e,t=!0){return Q(e)?e.displayName||e.name:e.name||t&&e.__name}function ei(e){return Q(e)&&"__vccOpts"in e}const se=(e,t)=>Jl(e,t,hn);function Jr(e,t,n){const s=arguments.length;return s===2?ce(t)&&!B(t)?pn(t)?z(e,null,[t]):z(e,t):z(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&pn(n)&&(n=[n]),z(e,t,n))}const ti="3.5.15";/**
* @vue/runtime-dom v3.5.15
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ws;const co=typeof window<"u"&&window.trustedTypes;if(co)try{ws=co.createPolicy("vue",{createHTML:e=>e})}catch{}const Wr=ws?e=>ws.createHTML(e):e=>e,ni="http://www.w3.org/2000/svg",si="http://www.w3.org/1998/Math/MathML",ct=typeof document<"u"?document:null,uo=ct&&ct.createElement("template"),oi={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const o=t==="svg"?ct.createElementNS(ni,e):t==="mathml"?ct.createElementNS(si,e):n?ct.createElement(e,{is:n}):ct.createElement(e);return e==="select"&&s&&s.multiple!=null&&o.setAttribute("multiple",s.multiple),o},createText:e=>ct.createTextNode(e),createComment:e=>ct.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ct.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,o,r){const l=n?n.previousSibling:t.lastChild;if(o&&(o===r||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),!(o===r||!(o=o.nextSibling)););else{uo.innerHTML=Wr(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const a=uo.content;if(s==="svg"||s==="mathml"){const i=a.firstChild;for(;i.firstChild;)a.appendChild(i.firstChild);a.removeChild(i)}t.insertBefore(a,n)}return[l?l.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},ri=Symbol("_vtc");function li(e,t,n){const s=e[ri];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const fo=Symbol("_vod"),ai=Symbol("_vsh"),ii=Symbol(""),ci=/(^|;)\s*display\s*:/;function ui(e,t,n){const s=e.style,o=ve(n);let r=!1;if(n&&!o){if(t)if(ve(t))for(const l of t.split(";")){const a=l.slice(0,l.indexOf(":")).trim();n[a]==null&&Dn(s,a,"")}else for(const l in t)n[l]==null&&Dn(s,l,"");for(const l in n)l==="display"&&(r=!0),Dn(s,l,n[l])}else if(o){if(t!==n){const l=s[ii];l&&(n+=";"+l),s.cssText=n,r=ci.test(n)}}else t&&e.removeAttribute("style");fo in e&&(e[fo]=r?s.display:"",e[ai]&&(s.display="none"))}const po=/\s*!important$/;function Dn(e,t,n){if(B(n))n.forEach(s=>Dn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=di(e,t);po.test(n)?e.setProperty(St(s),n.replace(po,""),"important"):e[s]=n}}const ho=["Webkit","Moz","ms"],os={};function di(e,t){const n=os[t];if(n)return n;let s=Be(t);if(s!=="filter"&&s in e)return os[t]=s;s=Rn(s);for(let o=0;o<ho.length;o++){const r=ho[o]+s;if(r in e)return os[t]=r}return t}const mo="http://www.w3.org/1999/xlink";function vo(e,t,n,s,o,r=_l(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(mo,t.slice(6,t.length)):e.setAttributeNS(mo,t,n):n==null||r&&!Jo(n)?e.removeAttribute(t):e.setAttribute(t,r?"":vt(n)?String(n):n)}function go(e,t,n,s,o){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Wr(n):n);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const a=r==="OPTION"?e.getAttribute("value")||"":e.value,i=n==null?e.type==="checkbox"?"on":"":String(n);(a!==i||!("_value"in e))&&(e.value=i),n==null&&e.removeAttribute(t),e._value=n;return}let l=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=Jo(n):n==null&&a==="string"?(n="",l=!0):a==="number"&&(n=0,l=!0)}try{e[t]=n}catch{}l&&e.removeAttribute(o||t)}function Tt(e,t,n,s){e.addEventListener(t,n,s)}function fi(e,t,n,s){e.removeEventListener(t,n,s)}const yo=Symbol("_vei");function pi(e,t,n,s,o=null){const r=e[yo]||(e[yo]={}),l=r[t];if(s&&l)l.value=s;else{const[a,i]=hi(t);if(s){const d=r[t]=gi(s,o);Tt(e,a,d,i)}else l&&(fi(e,a,l,i),r[t]=void 0)}}const bo=/(?:Once|Passive|Capture)$/;function hi(e){let t;if(bo.test(e)){t={};let n;for(;n=e.match(bo);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):St(e.slice(2)),t]}let rs=0;const mi=Promise.resolve(),vi=()=>rs||(mi.then(()=>rs=0),rs=Date.now());function gi(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;st(yi(s,n.value),t,5,[s])};return n.value=e,n.attached=vi(),n}function yi(e,t){if(B(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>o=>!o._stopped&&s&&s(o))}else return t}const _o=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,bi=(e,t,n,s,o,r)=>{const l=o==="svg";t==="class"?li(e,s,l):t==="style"?ui(e,n,s):Pn(t)?As(t)||pi(e,t,n,s,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):_i(e,t,s,l))?(go(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&vo(e,t,s,l,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ve(s))?go(e,Be(t),s,r,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),vo(e,t,s,l))};function _i(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&_o(t)&&Q(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return _o(t)&&ve(n)?!1:t in e}const wo=e=>{const t=e.props["onUpdate:modelValue"]||!1;return B(t)?n=>Sn(t,n):t};function wi(e){e.target.composing=!0}function xo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const ls=Symbol("_assign"),nt={created(e,{modifiers:{lazy:t,trim:n,number:s}},o){e[ls]=wo(o);const r=s||o.props&&o.props.type==="number";Tt(e,t?"change":"input",l=>{if(l.target.composing)return;let a=e.value;n&&(a=a.trim()),r&&(a=ds(a)),e[ls](a)}),n&&Tt(e,"change",()=>{e.value=e.value.trim()}),t||(Tt(e,"compositionstart",wi),Tt(e,"compositionend",xo),Tt(e,"change",xo))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:o,number:r}},l){if(e[ls]=wo(l),e.composing)return;const a=(r||e.type==="number")&&!/^0\d/.test(e.value)?ds(e.value):e.value,i=t??"";a!==i&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||o&&e.value.trim()===i)||(e.value=i))}},xi=["ctrl","shift","alt","meta"],ki={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>xi.some(n=>e[`${n}Key`]&&!t.includes(n))},mt=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(o,...r)=>{for(let l=0;l<t.length;l++){const a=ki[t[l]];if(a&&a(o,t))return}return e(o,...r)})},Ci={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},mn=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=o=>{if(!("key"in o))return;const r=St(o.key);if(t.some(l=>l===r||Ci[l]===r))return e(o)})},Si=Ce({patchProp:bi},oi);let ko;function Ai(){return ko||(ko=$a(Si))}const Di=(...e)=>{const t=Ai().createApp(...e),{mount:n}=t;return t.mount=s=>{const o=Ei(s);if(!o)return;const r=t._component;!Q(r)&&!r.render&&!r.template&&(r.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const l=n(o,!1,$i(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),l},t};function $i(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Ei(e){return ve(e)?document.querySelector(e):e}/*!
* vue-router v4.5.1
* (c) 2025 Eduardo San Martin Morote
* @license MIT
*/const It=typeof document<"u";function Gr(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function ji(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Gr(e.default)}const X=Object.assign;function as(e,t){const n={};for(const s in t){const o=t[s];n[s]=ze(o)?o.map(e):e(o)}return n}const ln=()=>{},ze=Array.isArray,Kr=/#/g,Oi=/&/g,Ni=/\//g,Fi=/=/g,Mi=/\?/g,Hr=/\+/g,Pi=/%5B/g,Ti=/%5D/g,Xr=/%5E/g,Ii=/%60/g,Yr=/%7B/g,Ri=/%7C/g,Zr=/%7D/g,Vi=/%20/g;function Bs(e){return encodeURI(""+e).replace(Ri,"|").replace(Pi,"[").replace(Ti,"]")}function Li(e){return Bs(e).replace(Yr,"{").replace(Zr,"}").replace(Xr,"^")}function xs(e){return Bs(e).replace(Hr,"%2B").replace(Vi,"+").replace(Kr,"%23").replace(Oi,"%26").replace(Ii,"`").replace(Yr,"{").replace(Zr,"}").replace(Xr,"^")}function Ui(e){return xs(e).replace(Fi,"%3D")}function Bi(e){return Bs(e).replace(Kr,"%23").replace(Mi,"%3F")}function qi(e){return e==null?"":Bi(e).replace(Ni,"%2F")}function vn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const zi=/\/$/,Qi=e=>e.replace(zi,"");function is(e,t,n="/"){let s,o={},r="",l="";const a=t.indexOf("#");let i=t.indexOf("?");return a<i&&a>=0&&(i=-1),i>-1&&(s=t.slice(0,i),r=t.slice(i+1,a>-1?a:t.length),o=e(r)),a>-1&&(s=s||t.slice(0,a),l=t.slice(a,t.length)),s=Ki(s??t,n),{fullPath:s+(r&&"?")+r+l,path:s,query:o,hash:vn(l)}}function Ji(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Co(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Wi(e,t,n){const s=t.matched.length-1,o=n.matched.length-1;return s>-1&&s===o&&Wt(t.matched[s],n.matched[o])&&el(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Wt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function el(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Gi(e[n],t[n]))return!1;return!0}function Gi(e,t){return ze(e)?So(e,t):ze(t)?So(t,e):e===t}function So(e,t){return ze(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Ki(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),o=s[s.length-1];(o===".."||o===".")&&s.push("");let r=n.length-1,l,a;for(l=0;l<s.length;l++)if(a=s[l],a!==".")if(a==="..")r>1&&r--;else break;return n.slice(0,r).join("/")+"/"+s.slice(l).join("/")}const bt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var gn;(function(e){e.pop="pop",e.push="push"})(gn||(gn={}));var an;(function(e){e.back="back",e.forward="forward",e.unknown=""})(an||(an={}));function Hi(e){if(!e)if(It){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Qi(e)}const Xi=/^[^#]+#/;function Yi(e,t){return e.replace(Xi,"#")+t}function Zi(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Wn=()=>({left:window.scrollX,top:window.scrollY});function ec(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=Zi(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Ao(e,t){return(history.state?history.state.position-t:-1)+e}const ks=new Map;function tc(e,t){ks.set(e,t)}function nc(e){const t=ks.get(e);return ks.delete(e),t}let sc=()=>location.protocol+"//"+location.host;function tl(e,t){const{pathname:n,search:s,hash:o}=t,r=e.indexOf("#");if(r>-1){let l=o.includes(e.slice(r))?e.slice(r).length:1,a=o.slice(l);return a[0]!=="/"&&(a="/"+a),Co(a,"")}return Co(n,e)+s+o}function oc(e,t,n,s){let o=[],r=[],l=null;const a=({state:h})=>{const m=tl(e,location),A=n.value,C=t.value;let M=0;if(h){if(n.value=m,t.value=h,l&&l===A){l=null;return}M=C?h.position-C.position:0}else s(m);o.forEach(R=>{R(n.value,A,{delta:M,type:gn.pop,direction:M?M>0?an.forward:an.back:an.unknown})})};function i(){l=n.value}function d(h){o.push(h);const m=()=>{const A=o.indexOf(h);A>-1&&o.splice(A,1)};return r.push(m),m}function c(){const{history:h}=window;h.state&&h.replaceState(X({},h.state,{scroll:Wn()}),"")}function f(){for(const h of r)h();r=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:i,listen:d,destroy:f}}function Do(e,t,n,s=!1,o=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:o?Wn():null}}function rc(e){const{history:t,location:n}=window,s={value:tl(e,n)},o={value:t.state};o.value||r(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function r(i,d,c){const f=e.indexOf("#"),h=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+i:sc()+e+i;try{t[c?"replaceState":"pushState"](d,"",h),o.value=d}catch(m){console.error(m),n[c?"replace":"assign"](h)}}function l(i,d){const c=X({},t.state,Do(o.value.back,i,o.value.forward,!0),d,{position:o.value.position});r(i,c,!0),s.value=i}function a(i,d){const c=X({},o.value,t.state,{forward:i,scroll:Wn()});r(c.current,c,!0);const f=X({},Do(s.value,i,null),{position:c.position+1},d);r(i,f,!1),s.value=i}return{location:s,state:o,push:a,replace:l}}function lc(e){e=Hi(e);const t=rc(e),n=oc(e,t.state,t.location,t.replace);function s(r,l=!0){l||n.pauseListeners(),history.go(r)}const o=X({location:"",base:e,go:s,createHref:Yi.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function ac(e){return typeof e=="string"||e&&typeof e=="object"}function nl(e){return typeof e=="string"||typeof e=="symbol"}const sl=Symbol("");var $o;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})($o||($o={}));function Gt(e,t){return X(new Error,{type:e,[sl]:!0},t)}function it(e,t){return e instanceof Error&&sl in e&&(t==null||!!(e.type&t))}const Eo="[^/]+?",ic={sensitive:!1,strict:!1,start:!0,end:!0},cc=/[.+*?^${}()[\]/\\]/g;function uc(e,t){const n=X({},ic,t),s=[];let o=n.start?"^":"";const r=[];for(const d of e){const c=d.length?[]:[90];n.strict&&!d.length&&(o+="/");for(let f=0;f<d.length;f++){const h=d[f];let m=40+(n.sensitive?.25:0);if(h.type===0)f||(o+="/"),o+=h.value.replace(cc,"\\$&"),m+=40;else if(h.type===1){const{value:A,repeatable:C,optional:M,regexp:R}=h;r.push({name:A,repeatable:C,optional:M});const P=R||Eo;if(P!==Eo){m+=10;try{new RegExp(`(${P})`)}catch(T){throw new Error(`Invalid custom RegExp for param "${A}" (${P}): `+T.message)}}let I=C?`((?:${P})(?:/(?:${P}))*)`:`(${P})`;f||(I=M&&d.length<2?`(?:/${I})`:"/"+I),M&&(I+="?"),o+=I,m+=20,M&&(m+=-8),C&&(m+=-20),P===".*"&&(m+=-50)}c.push(m)}s.push(c)}if(n.strict&&n.end){const d=s.length-1;s[d][s[d].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const l=new RegExp(o,n.sensitive?"":"i");function a(d){const c=d.match(l),f={};if(!c)return null;for(let h=1;h<c.length;h++){const m=c[h]||"",A=r[h-1];f[A.name]=m&&A.repeatable?m.split("/"):m}return f}function i(d){let c="",f=!1;for(const h of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const m of h)if(m.type===0)c+=m.value;else if(m.type===1){const{value:A,repeatable:C,optional:M}=m,R=A in d?d[A]:"";if(ze(R)&&!C)throw new Error(`Provided param "${A}" is an array but it is not repeatable (* or + modifiers)`);const P=ze(R)?R.join("/"):R;if(!P)if(M)h.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${A}"`);c+=P}}return c||"/"}return{re:l,score:s,keys:r,parse:a,stringify:i}}function dc(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function ol(e,t){let n=0;const s=e.score,o=t.score;for(;n<s.length&&n<o.length;){const r=dc(s[n],o[n]);if(r)return r;n++}if(Math.abs(o.length-s.length)===1){if(jo(s))return 1;if(jo(o))return-1}return o.length-s.length}function jo(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const fc={type:0,value:""},pc=/[a-zA-Z0-9_]/;function hc(e){if(!e)return[[]];if(e==="/")return[[fc]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${d}": ${m}`)}let n=0,s=n;const o=[];let r;function l(){r&&o.push(r),r=[]}let a=0,i,d="",c="";function f(){d&&(n===0?r.push({type:0,value:d}):n===1||n===2||n===3?(r.length>1&&(i==="*"||i==="+")&&t(`A repeatable param (${d}) must be alone in its segment. eg: '/:ids+.`),r.push({type:1,value:d,regexp:c,repeatable:i==="*"||i==="+",optional:i==="*"||i==="?"})):t("Invalid state to consume buffer"),d="")}function h(){d+=i}for(;a<e.length;){if(i=e[a++],i==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:i==="/"?(d&&f(),l()):i===":"?(f(),n=1):h();break;case 4:h(),n=s;break;case 1:i==="("?n=2:pc.test(i)?h():(f(),n=0,i!=="*"&&i!=="?"&&i!=="+"&&a--);break;case 2:i===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+i:n=3:c+=i;break;case 3:f(),n=0,i!=="*"&&i!=="?"&&i!=="+"&&a--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${d}"`),f(),l(),o}function mc(e,t,n){const s=uc(hc(e.path),n),o=X(s,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function vc(e,t){const n=[],s=new Map;t=Mo({strict:!1,end:!0,sensitive:!1},t);function o(f){return s.get(f)}function r(f,h,m){const A=!m,C=No(f);C.aliasOf=m&&m.record;const M=Mo(t,f),R=[C];if("alias"in f){const T=typeof f.alias=="string"?[f.alias]:f.alias;for(const H of T)R.push(No(X({},C,{components:m?m.record.components:C.components,path:H,aliasOf:m?m.record:C})))}let P,I;for(const T of R){const{path:H}=T;if(h&&H[0]!=="/"){const ge=h.record.path,le=ge[ge.length-1]==="/"?"":"/";T.path=h.record.path+(H&&le+H)}if(P=mc(T,h,M),m?m.alias.push(P):(I=I||P,I!==P&&I.alias.push(P),A&&f.name&&!Fo(P)&&l(f.name)),rl(P)&&i(P),C.children){const ge=C.children;for(let le=0;le<ge.length;le++)r(ge[le],P,m&&m.children[le])}m=m||P}return I?()=>{l(I)}:ln}function l(f){if(nl(f)){const h=s.get(f);h&&(s.delete(f),n.splice(n.indexOf(h),1),h.children.forEach(l),h.alias.forEach(l))}else{const h=n.indexOf(f);h>-1&&(n.splice(h,1),f.record.name&&s.delete(f.record.name),f.children.forEach(l),f.alias.forEach(l))}}function a(){return n}function i(f){const h=bc(f,n);n.splice(h,0,f),f.record.name&&!Fo(f)&&s.set(f.record.name,f)}function d(f,h){let m,A={},C,M;if("name"in f&&f.name){if(m=s.get(f.name),!m)throw Gt(1,{location:f});M=m.record.name,A=X(Oo(h.params,m.keys.filter(I=>!I.optional).concat(m.parent?m.parent.keys.filter(I=>I.optional):[]).map(I=>I.name)),f.params&&Oo(f.params,m.keys.map(I=>I.name))),C=m.stringify(A)}else if(f.path!=null)C=f.path,m=n.find(I=>I.re.test(C)),m&&(A=m.parse(C),M=m.record.name);else{if(m=h.name?s.get(h.name):n.find(I=>I.re.test(h.path)),!m)throw Gt(1,{location:f,currentLocation:h});M=m.record.name,A=X({},h.params,f.params),C=m.stringify(A)}const R=[];let P=m;for(;P;)R.unshift(P.record),P=P.parent;return{name:M,path:C,params:A,matched:R,meta:yc(R)}}e.forEach(f=>r(f));function c(){n.length=0,s.clear()}return{addRoute:r,resolve:d,removeRoute:l,clearRoutes:c,getRoutes:a,getRecordMatcher:o}}function Oo(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function No(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:gc(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function gc(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Fo(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function yc(e){return e.reduce((t,n)=>X(t,n.meta),{})}function Mo(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function bc(e,t){let n=0,s=t.length;for(;n!==s;){const r=n+s>>1;ol(e,t[r])<0?s=r:n=r+1}const o=_c(e);return o&&(s=t.lastIndexOf(o,s-1)),s}function _c(e){let t=e;for(;t=t.parent;)if(rl(t)&&ol(e,t)===0)return t}function rl({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function wc(e){const t={};if(e===""||e==="?")return t;const n=(e[0]==="?"?e.slice(1):e).split("&");for(let s=0;s<n.length;++s){const o=n[s].replace(Hr," "),r=o.indexOf("="),l=vn(r<0?o:o.slice(0,r)),a=r<0?null:vn(o.slice(r+1));if(l in t){let i=t[l];ze(i)||(i=t[l]=[i]),i.push(a)}else t[l]=a}return t}function Po(e){let t="";for(let n in e){const s=e[n];if(n=Ui(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(ze(s)?s.map(o=>o&&xs(o)):[s&&xs(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function xc(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=ze(s)?s.map(o=>o==null?null:""+o):s==null?s:""+s)}return t}const kc=Symbol(""),To=Symbol(""),Gn=Symbol(""),ll=Symbol(""),Cs=Symbol("");function Zt(){let e=[];function t(s){return e.push(s),()=>{const o=e.indexOf(s);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function xt(e,t,n,s,o,r=l=>l()){const l=s&&(s.enterCallbacks[o]=s.enterCallbacks[o]||[]);return()=>new Promise((a,i)=>{const d=h=>{h===!1?i(Gt(4,{from:n,to:t})):h instanceof Error?i(h):ac(h)?i(Gt(2,{from:t,to:h})):(l&&s.enterCallbacks[o]===l&&typeof h=="function"&&l.push(h),a())},c=r(()=>e.call(s&&s.instances[o],t,n,d));let f=Promise.resolve(c);e.length<3&&(f=f.then(d)),f.catch(h=>i(h))})}function cs(e,t,n,s,o=r=>r()){const r=[];for(const l of e)for(const a in l.components){let i=l.components[a];if(!(t!=="beforeRouteEnter"&&!l.instances[a]))if(Gr(i)){const d=(i.__vccOpts||i)[t];d&&r.push(xt(d,n,s,l,a,o))}else{let d=i();r.push(()=>d.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${a}" at "${l.path}"`);const f=ji(c)?c.default:c;l.mods[a]=c,l.components[a]=f;const h=(f.__vccOpts||f)[t];return h&&xt(h,n,s,l,a,o)()}))}}return r}function Io(e){const t=Ue(Gn),n=Ue(ll),s=se(()=>{const i=D(e.to);return t.resolve(i)}),o=se(()=>{const{matched:i}=s.value,{length:d}=i,c=i[d-1],f=n.matched;if(!c||!f.length)return-1;const h=f.findIndex(Wt.bind(null,c));if(h>-1)return h;const m=Ro(i[d-2]);return d>1&&Ro(c)===m&&f[f.length-1].path!==m?f.findIndex(Wt.bind(null,i[d-2])):h}),r=se(()=>o.value>-1&&$c(n.params,s.value.params)),l=se(()=>o.value>-1&&o.value===n.matched.length-1&&el(n.params,s.value.params));function a(i={}){if(Dc(i)){const d=t[D(e.replace)?"replace":"push"](D(e.to)).catch(ln);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>d),d}return Promise.resolve()}return{route:s,href:se(()=>s.value.href),isActive:r,isExactActive:l,navigate:a}}function Cc(e){return e.length===1?e[0]:e}const Sc=yr({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Io,setup(e,{slots:t}){const n=Ot(Io(e)),{options:s}=Ue(Gn),o=se(()=>({[Vo(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[Vo(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const r=t.default&&Cc(t.default(n));return e.custom?r:Jr("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},r)}}}),Ac=Sc;function Dc(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function $c(e,t){for(const n in t){const s=t[n],o=e[n];if(typeof s=="string"){if(s!==o)return!1}else if(!ze(o)||o.length!==s.length||s.some((r,l)=>r!==o[l]))return!1}return!0}function Ro(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Vo=(e,t,n)=>e??t??n,Ec=yr({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=Ue(Cs),o=se(()=>e.route||s.value),r=Ue(To,0),l=se(()=>{let d=D(r);const{matched:c}=o.value;let f;for(;(f=c[d])&&!f.components;)d++;return d}),a=se(()=>o.value.matched[l.value]);Qt(To,se(()=>l.value+1)),Qt(kc,a),Qt(Cs,o);const i=K();return tt(()=>[i.value,a.value,e.name],([d,c,f],[h,m,A])=>{c&&(c.instances[f]=d,m&&m!==c&&d&&d===h&&(c.leaveGuards.size||(c.leaveGuards=m.leaveGuards),c.updateGuards.size||(c.updateGuards=m.updateGuards))),d&&c&&(!m||!Wt(c,m)||!h)&&(c.enterCallbacks[f]||[]).forEach(C=>C(d))},{flush:"post"}),()=>{const d=o.value,c=e.name,f=a.value,h=f&&f.components[c];if(!h)return Lo(n.default,{Component:h,route:d});const m=f.props[c],A=m?m===!0?d.params:typeof m=="function"?m(d):m:null,C=Jr(h,X({},A,t,{onVnodeUnmounted:M=>{M.component.isUnmounted&&(f.instances[c]=null)},ref:i}));return Lo(n.default,{Component:C,route:d})||C}}});function Lo(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const jc=Ec;function Oc(e){const t=vc(e.routes,e),n=e.parseQuery||wc,s=e.stringifyQuery||Po,o=e.history,r=Zt(),l=Zt(),a=Zt(),i=Bl(bt);let d=bt;It&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=as.bind(null,_=>""+_),f=as.bind(null,qi),h=as.bind(null,vn);function m(_,F){let j,L;return nl(_)?(j=t.getRecordMatcher(_),L=F):L=_,t.addRoute(L,j)}function A(_){const F=t.getRecordMatcher(_);F&&t.removeRoute(F)}function C(){return t.getRoutes().map(_=>_.record)}function M(_){return!!t.getRecordMatcher(_)}function R(_,F){if(F=X({},F||i.value),typeof _=="string"){const y=is(n,_,F.path),b=t.resolve({path:y.path},F),w=o.createHref(y.fullPath);return X(y,b,{params:h(b.params),hash:vn(y.hash),redirectedFrom:void 0,href:w})}let j;if(_.path!=null)j=X({},_,{path:is(n,_.path,F.path).path});else{const y=X({},_.params);for(const b in y)y[b]==null&&delete y[b];j=X({},_,{params:f(y)}),F.params=f(F.params)}const L=t.resolve(j,F),u=_.hash||"";L.params=c(h(L.params));const p=Ji(s,X({},_,{hash:Li(u),path:L.path})),v=o.createHref(p);return X({fullPath:p,hash:u,query:s===Po?xc(_.query):_.query||{}},L,{redirectedFrom:void 0,href:v})}function P(_){return typeof _=="string"?is(n,_,i.value.path):X({},_)}function I(_,F){if(d!==_)return Gt(8,{from:F,to:_})}function T(_){return le(_)}function H(_){return T(X(P(_),{replace:!0}))}function ge(_){const F=_.matched[_.matched.length-1];if(F&&F.redirect){const{redirect:j}=F;let L=typeof j=="function"?j(_):j;return typeof L=="string"&&(L=L.includes("?")||L.includes("#")?L=P(L):{path:L},L.params={}),X({query:_.query,hash:_.hash,params:L.path!=null?{}:_.params},L)}}function le(_,F){const j=d=R(_),L=i.value,u=_.state,p=_.force,v=_.replace===!0,y=ge(j);if(y)return le(X(P(y),{state:typeof y=="object"?X({},u,y.state):u,force:p,replace:v}),F||j);const b=j;b.redirectedFrom=F;let w;return!p&&Wi(s,L,j)&&(w=Gt(16,{to:b,from:L}),lt(L,L,!0,!1)),(w?Promise.resolve(w):Te(b,L)).catch(S=>it(S)?it(S,2)?S:rt(S):fe(S,b,L)).then(S=>{if(S){if(it(S,2))return le(X({replace:v},P(S.to),{state:typeof S.to=="object"?X({},u,S.to.state):u,force:p}),F||b)}else S=Ie(b,L,!0,v,u);return Je(b,L,S),S})}function je(_,F){const j=I(_,F);return j?Promise.reject(j):Promise.resolve()}function Qe(_){const F=Ft.values().next().value;return F&&typeof F.runWithContext=="function"?F.runWithContext(_):_()}function Te(_,F){let j;const[L,u,p]=Nc(_,F);j=cs(L.reverse(),"beforeRouteLeave",_,F);for(const y of L)y.leaveGuards.forEach(b=>{j.push(xt(b,_,F))});const v=je.bind(null,_,F);return j.push(v),Ve(j).then(()=>{j=[];for(const y of r.list())j.push(xt(y,_,F));return j.push(v),Ve(j)}).then(()=>{j=cs(u,"beforeRouteUpdate",_,F);for(const y of u)y.updateGuards.forEach(b=>{j.push(xt(b,_,F))});return j.push(v),Ve(j)}).then(()=>{j=[];for(const y of p)if(y.beforeEnter)if(ze(y.beforeEnter))for(const b of y.beforeEnter)j.push(xt(b,_,F));else j.push(xt(y.beforeEnter,_,F));return j.push(v),Ve(j)}).then(()=>(_.matched.forEach(y=>y.enterCallbacks={}),j=cs(p,"beforeRouteEnter",_,F,Qe),j.push(v),Ve(j))).then(()=>{j=[];for(const y of l.list())j.push(xt(y,_,F));return j.push(v),Ve(j)}).catch(y=>it(y,8)?y:Promise.reject(y))}function Je(_,F,j){a.list().forEach(L=>Qe(()=>L(_,F,j)))}function Ie(_,F,j,L,u){const p=I(_,F);if(p)return p;const v=F===bt,y=It?history.state:{};j&&(L||v?o.replace(_.fullPath,X({scroll:v&&y&&y.scroll},u)):o.push(_.fullPath,u)),i.value=_,lt(_,F,j,v),rt()}let ye;function At(){ye||(ye=o.listen((_,F,j)=>{if(!_n.listening)return;const L=R(_),u=ge(L);if(u){le(X(u,{replace:!0,force:!0}),L).catch(ln);return}d=L;const p=i.value;It&&tc(Ao(p.fullPath,j.delta),Wn()),Te(L,p).catch(v=>it(v,12)?v:it(v,2)?(le(X(P(v.to),{force:!0}),L).then(y=>{it(y,20)&&!j.delta&&j.type===gn.pop&&o.go(-1,!1)}).catch(ln),Promise.reject()):(j.delta&&o.go(-j.delta,!1),fe(v,L,p))).then(v=>{v=v||Ie(L,p,!1),v&&(j.delta&&!it(v,8)?o.go(-j.delta,!1):j.type===gn.pop&&it(v,20)&&o.go(-1,!1)),Je(L,p,v)}).catch(ln)}))}let he=Zt(),te=Zt(),W;function fe(_,F,j){rt(_);const L=te.list();return L.length?L.forEach(u=>u(_,F,j)):console.error(_),Promise.reject(_)}function Kt(){return W&&i.value!==bt?Promise.resolve():new Promise((_,F)=>{he.add([_,F])})}function rt(_){return W||(W=!_,At(),he.list().forEach(([F,j])=>_?j(_):F()),he.reset()),_}function lt(_,F,j,L){const{scrollBehavior:u}=e;if(!It||!u)return Promise.resolve();const p=!j&&nc(Ao(_.fullPath,0))||(L||!j)&&history.state&&history.state.scroll||null;return Ts().then(()=>u(_,F,p)).then(v=>v&&ec(v)).catch(v=>fe(v,_,F))}const Re=_=>o.go(_);let Nt;const Ft=new Set,_n={currentRoute:i,listening:!0,addRoute:m,removeRoute:A,clearRoutes:t.clearRoutes,hasRoute:M,getRoutes:C,resolve:R,options:e,push:T,replace:H,go:Re,back:()=>Re(-1),forward:()=>Re(1),beforeEach:r.add,beforeResolve:l.add,afterEach:a.add,onError:te.add,isReady:Kt,install(_){const F=this;_.component("RouterLink",Ac),_.component("RouterView",jc),_.config.globalProperties.$router=F,Object.defineProperty(_.config.globalProperties,"$route",{enumerable:!0,get:()=>D(i)}),It&&!Nt&&i.value===bt&&(Nt=!0,T(o.location).catch(u=>{}));const j={};for(const u in bt)Object.defineProperty(j,u,{get:()=>i.value[u],enumerable:!0});_.provide(Gn,F),_.provide(ll,cr(j)),_.provide(Cs,i);const L=_.unmount;Ft.add(_),_.unmount=function(){Ft.delete(_),Ft.size<1&&(d=bt,ye&&ye(),ye=null,i.value=bt,Nt=!1,W=!1),L()}}};function Ve(_){return _.reduce((F,j)=>F.then(()=>Qe(j)),Promise.resolve())}return _n}function Nc(e,t){const n=[],s=[],o=[],r=Math.max(t.matched.length,e.matched.length);for(let l=0;l<r;l++){const a=t.matched[l];a&&(e.matched.find(d=>Wt(d,a))?s.push(a):n.push(a));const i=e.matched[l];i&&(t.matched.find(d=>Wt(d,i))||o.push(i))}return[n,s,o]}function qs(){return Ue(Gn)}function al(){const e=K([{id:1,message:"Mr A updated a document (Click to view)",classId:1,documentId:1,timestamp:new Date(Date.now()-18e5),read:!1},{id:2,message:"Mr B updated a document (Click to view)",classId:2,documentId:2,timestamp:new Date(Date.now()-72e5),read:!1}]),t=se(()=>e.value.filter(s=>!s.read).length),n=se(()=>[...e.value].sort((s,o)=>o.timestamp-s.timestamp));return{notifications:e,unreadCount:t,sortedNotifications:n,markAsRead:s=>{const o=e.value.find(r=>r.id===s);o&&(o.read=!0)},markAllAsRead:()=>{e.value.forEach(s=>s.read=!0)},addNotification:(s,o,r)=>{const l={id:Date.now(),message:s,classId:o,documentId:r,timestamp:new Date,read:!1};return e.value.unshift(l),l},removeNotification:s=>{const o=e.value.findIndex(r=>r.id===s);o>-1&&e.value.splice(o,1)}}}const ot=(e,t)=>{const n=e.__vccOpts||e;for(const[s,o]of t)n[s]=o;return n},Fc={key:0,class:"card-header d-flex justify-content-between align-items-center bg-violet"},Mc={class:"card-title h5 mb-0 text-white"},Pc={class:"card-body p-3"},Tc={key:1,class:"card-footer"},Ic={__name:"BaseCard",props:{title:{type:String,default:""},variant:{type:String,default:"default",validator:e=>["default","primary","secondary","success","danger","warning","info","light","dark"].includes(e)},clickable:{type:Boolean,default:!1},selected:{type:Boolean,default:!1}},emits:["click"],setup(e,{emit:t}){const n=e,s=t,o=se(()=>{const l=["card","h-100"];return n.clickable&&l.push("card-clickable"),n.selected&&l.push("border-primary","bg-primary","bg-opacity-10"),l.join(" ")}),r=l=>{n.clickable&&s("click",l)};return(l,a)=>(x(),O("div",{class:Ee(o.value),onClick:r},[l.$slots.header||e.title?(x(),O("div",Fc,[Rt(l.$slots,"header",{},()=>[g("h2",Mc,J(e.title),1)]),Rt(l.$slots,"header-actions",{},void 0)])):oe("",!0),g("div",Pc,[Rt(l.$slots,"default",{},void 0)]),l.$slots.footer?(x(),O("div",Tc,[Rt(l.$slots,"footer",{},void 0)])):oe("",!0)],2))}},yt=ot(Ic,[["__scopeId","data-v-0b85be82"]]),Rc=["disabled"],Vc={__name:"BaseButton",props:{variant:{type:String,default:"primary",validator:e=>["primary","secondary","success","danger","warning","info","light","dark","outline-primary","outline-secondary","outline-success","outline-danger","outline-warning","outline-info","outline-light","outline-dark","purple"].includes(e)},size:{type:String,default:"md",validator:e=>["sm","md","lg"].includes(e)},disabled:{type:Boolean,default:!1},icon:{type:String,default:"",validator:e=>!e||e.startsWith("bi-")}},emits:["click"],setup(e){const t=e,n=se(()=>{const s=["btn"];return t.variant==="purple"?s.push("btn-purple"):s.push(`btn-${t.variant}`),t.size!=="md"&&s.push(`btn-${t.size}`),s.join(" ")});return(s,o)=>(x(),O("button",{class:Ee(n.value),disabled:e.disabled,onClick:o[0]||(o[0]=r=>s.$emit("click",r))},[e.icon?(x(),O("i",{key:0,class:Ee([`bi ${e.icon}`,{"me-2":s.$slots.default}]),"aria-hidden":"true"},null,2)):oe("",!0),Rt(s.$slots,"default",{},void 0)],10,Rc))}},pe=ot(Vc,[["__scopeId","data-v-ac23806e"]]);function Kn(){const e=Ue("selectedClass"),t=Ue("selectClass"),n=K([]),s=K([]),o=K([]),r=Ot({subjects:["math","english","science"],classAverage:[8.2,7.5,8.8],reachedNorm:["yes","no","yes"],passingPerc:["85%","72%","91%"]}),l=se(()=>e.value?s.value.filter(m=>m.classId===e.value.id):[]),a=se(()=>e.value?o.value.filter(m=>m.classId===e.value.id):[]),i=se(()=>e.value?r:{subjects:[],classAverage:[],reachedNorm:[],passingPerc:[]}),d=m=>{const A={id:Date.now(),name:m.name,teacher:`${m.teacherFirstName} ${m.teacherLastName}`};return n.value.push(A),A},c=m=>{if(!e.value)return null;const A={id:Date.now(),name:m.trim(),classId:e.value.id};return s.value.push(A),A},f=m=>{if(!e.value)return null;const A={id:Date.now(),title:m.title,lastEdited:new Date().toLocaleDateString("en-GB"),editor:m.editor,classId:e.value.id};return o.value.push(A),A},h=()=>{n.value=[{id:1,name:"Class 5",teacher:"Mr. Smith"},{id:2,name:"Class 6",teacher:"Ms. Johnson"}],s.value=[{id:1,name:"Jimmy Studentname",classId:1},{id:2,name:"Ash Ketchum",classId:1},{id:3,name:"Pikachu",classId:1},{id:4,name:"Brock Harrison",classId:1},{id:5,name:"Girl Name",classId:1},{id:6,name:"John Doe",classId:2},{id:7,name:"Jane Smith",classId:2}],o.value=[{id:1,title:"May 2025",lastEdited:"15/01/2025",editor:"Mr. Smith",classId:1},{id:2,title:"April 2025",lastEdited:"10/01/2025",editor:"Mr. Smith",classId:1},{id:3,title:"June 2025",lastEdited:"12/01/2025",editor:"Ms. Johnson",classId:2}]};return n.value.length===0&&h(),{classesData:n,studentsData:s,documentsData:o,statisticsData:r,selectedClass:e,filteredStudents:l,filteredDocuments:a,filteredStatistics:i,addClass:d,addStudent:c,addDocument:f,handleClassSelect:m=>{t(m)}}}const Lc={class:"mb-3"},Uc={class:"row"},Bc={class:"col-md-6 mb-3"},qc={class:"col-md-6 mb-3"},zc={class:"d-flex gap-2"},Qc={__name:"ClassForm",emits:["submit","cancel"],setup(e,{emit:t}){const n=t,s=Ot({name:"",teacherFirstName:"",teacherLastName:""}),o=()=>{s.name&&s.teacherFirstName&&s.teacherLastName&&(n("submit",{...s}),s.name="",s.teacherFirstName="",s.teacherLastName="")};return(r,l)=>(x(),de(yt,{title:"Add New Class"},{default:ee(()=>[g("form",{onSubmit:mt(o,["prevent"])},[g("div",Lc,[et(g("input",{"onUpdate:modelValue":l[0]||(l[0]=a=>s.name=a),placeholder:"Class name (e.g., class 5)",class:"form-control",required:""},null,512),[[nt,s.name]])]),g("div",Uc,[g("div",Bc,[et(g("input",{"onUpdate:modelValue":l[1]||(l[1]=a=>s.teacherFirstName=a),placeholder:"Teacher first name",class:"form-control",required:""},null,512),[[nt,s.teacherFirstName]])]),g("div",qc,[et(g("input",{"onUpdate:modelValue":l[2]||(l[2]=a=>s.teacherLastName=a),placeholder:"Teacher last name",class:"form-control",required:""},null,512),[[nt,s.teacherLastName]])])]),g("div",zc,[z(pe,{type:"submit",variant:"purple",icon:"bi-plus-lg"},{default:ee(()=>l[4]||(l[4]=[me(" Add Class ")])),_:1,__:[4]}),z(pe,{type:"button",variant:"secondary",icon:"bi-x-lg",onClick:l[3]||(l[3]=a=>r.$emit("cancel"))},{default:ee(()=>l[5]||(l[5]=[me(" Cancel ")])),_:1,__:[5]})])],32)]),_:1}))}},Jc={class:"classes-content"},Wc={key:1,class:"row g-2"},Gc={class:"card-title mb-1"},Kc={class:"card-text small text-muted mb-0"},Hc={key:2,class:"text-center text-muted py-4"},Xc={__name:"ClassesContent",props:{showAddForm:{type:Boolean,default:!1}},emits:["form-visibility-change"],setup(e,{emit:t}){const n=e,s=t,{classesData:o,selectedClass:r,addClass:l,handleClassSelect:a}=Kn(),i=K(n.showAddForm);tt(()=>n.showAddForm,c=>{i.value=c}),tt(i,c=>{s("form-visibility-change",c)});const d=c=>{l(c),i.value=!1};return(c,f)=>(x(),O("div",Jc,[i.value?(x(),de(Qc,{key:0,onSubmit:d,onCancel:f[0]||(f[0]=h=>i.value=!1),class:"mb-3"})):oe("",!0),D(o).length>0?(x(),O("div",Wc,[(x(!0),O(ie,null,Me(D(o),h=>(x(),O("div",{key:h.id,class:"col-md-6"},[z(yt,{clickable:!0,selected:D(r)&&D(r).id===h.id,onClick:m=>D(a)(h)},{default:ee(()=>[g("h6",Gc,J(h.name),1),g("p",Kc,J(h.teacher),1)]),_:2},1032,["selected","onClick"])]))),128))])):oe("",!0),D(o).length===0&&!i.value?(x(),O("div",Hc,f[1]||(f[1]=[g("p",{class:"mb-0"},"No classes added yet.",-1)]))):oe("",!0)]))}},Yc={class:"mb-3"},Zc={class:"d-flex gap-2"},eu={__name:"StudentForm",emits:["submit","cancel"],setup(e,{emit:t}){const n=t,s=K(""),o=()=>{s.value.trim()&&(n("submit",s.value.trim()),s.value="")};return(r,l)=>(x(),de(yt,{title:"Add New Student"},{default:ee(()=>[g("form",{onSubmit:mt(o,["prevent"])},[g("div",Yc,[et(g("input",{"onUpdate:modelValue":l[0]||(l[0]=a=>s.value=a),placeholder:"Student name (e.g., John Doe)",class:"form-control",required:""},null,512),[[nt,s.value]])]),g("div",Zc,[z(pe,{type:"submit",variant:"purple",icon:"bi-person-plus"},{default:ee(()=>l[2]||(l[2]=[me(" Add Student ")])),_:1,__:[2]}),z(pe,{type:"button",variant:"secondary",icon:"bi-x-lg",onClick:l[1]||(l[1]=a=>r.$emit("cancel"))},{default:ee(()=>l[3]||(l[3]=[me(" Cancel ")])),_:1,__:[3]})])],32)]),_:1}))}},tu={class:"students-content"},nu={key:1,class:"row g-2"},su={class:"text-center"},ou={class:"small"},ru={key:2,class:"text-center text-muted py-4"},lu={key:3,class:"text-center text-muted py-4"},au={class:"mb-0"},iu={__name:"StudentsContent",props:{showAddForm:{type:Boolean,default:!1}},emits:["form-visibility-change"],setup(e,{emit:t}){const n=e,s=t,{filteredStudents:o,selectedClass:r,addStudent:l}=Kn(),a=K(n.showAddForm);tt(()=>n.showAddForm,d=>{a.value=d}),tt(a,d=>{s("form-visibility-change",d)});const i=d=>{if(!r.value){alert("Please select a class first before adding students.");return}l(d),a.value=!1};return(d,c)=>(x(),O("div",tu,[a.value?(x(),de(eu,{key:0,onSubmit:i,onCancel:c[0]||(c[0]=f=>a.value=!1),class:"mb-3"})):oe("",!0),D(o).length>0?(x(),O("div",nu,[(x(!0),O(ie,null,Me(D(o),f=>(x(),O("div",{key:f.id,class:"col-md-6"},[z(yt,null,{default:ee(()=>[g("div",su,[g("span",ou,J(f.name),1)])]),_:2},1024)]))),128))])):oe("",!0),!D(r)&&!a.value?(x(),O("div",ru,c[1]||(c[1]=[g("p",{class:"mb-0"},"Please select a class first to view and add students.",-1)]))):D(r)&&D(o).length===0&&!a.value?(x(),O("div",lu,[g("p",au,"No students in "+J(D(r).name)+' yet. Click "+Add Student" to get started.',1)])):oe("",!0)]))}},cu={class:"mb-3"},uu={class:"mb-3"},du={class:"d-flex gap-2"},fu={__name:"DocumentForm",emits:["submit","cancel"],setup(e,{emit:t}){const n=t,s=Ot({title:"",editor:""}),o=()=>{s.title&&s.editor&&(n("submit",{...s}),s.title="",s.editor="")};return(r,l)=>(x(),de(yt,{title:"Add New Document"},{default:ee(()=>[g("form",{onSubmit:mt(o,["prevent"])},[g("div",cu,[et(g("input",{"onUpdate:modelValue":l[0]||(l[0]=a=>s.title=a),placeholder:"Document title (e.g., May 2025)",class:"form-control",required:""},null,512),[[nt,s.title]])]),g("div",uu,[et(g("input",{"onUpdate:modelValue":l[1]||(l[1]=a=>s.editor=a),placeholder:"Editor name (e.g., Mr. Smith)",class:"form-control",required:""},null,512),[[nt,s.editor]])]),g("div",du,[z(pe,{type:"submit",variant:"purple",icon:"bi-file-plus"},{default:ee(()=>l[3]||(l[3]=[me(" Add Document ")])),_:1,__:[3]}),z(pe,{type:"button",variant:"secondary",icon:"bi-x-lg",onClick:l[2]||(l[2]=a=>r.$emit("cancel"))},{default:ee(()=>l[4]||(l[4]=[me(" Cancel ")])),_:1,__:[4]})])],32)]),_:1}))}},pu={class:"documents-content"},hu={key:1,class:"list-group"},mu=["onClick"],vu={class:"d-flex w-100 justify-content-between"},gu={class:"mb-1"},yu={class:"text-muted"},bu={key:2,class:"text-center text-muted py-4"},_u={key:3,class:"text-center text-muted py-4"},wu={class:"mb-0"},xu={__name:"DocumentsContent",props:{showAddForm:{type:Boolean,default:!1}},emits:["form-visibility-change"],setup(e,{emit:t}){const n=qs(),s=e,o=t,{filteredDocuments:r,selectedClass:l,addDocument:a}=Kn(),i=K(s.showAddForm);tt(()=>s.showAddForm,f=>{i.value=f}),tt(i,f=>{o("form-visibility-change",f)});const d=f=>{if(!l.value){alert("Please select a class first before adding documents.");return}a(f),i.value=!1},c=f=>{if(!l.value){alert("Please select a class first.");return}n.push(`/document/${l.value.id}/${f.id}`)};return(f,h)=>(x(),O("div",pu,[i.value?(x(),de(fu,{key:0,onSubmit:d,onCancel:h[0]||(h[0]=m=>i.value=!1),class:"mb-3"})):oe("",!0),D(r).length>0?(x(),O("div",hu,[(x(!0),O(ie,null,Me(D(r),m=>(x(),O("div",{key:m.id,class:"list-group-item list-group-item-action cursor-pointer",onClick:A=>c(m)},[g("div",vu,[g("h6",gu,J(m.title),1),g("small",null,J(m.lastEdited),1)]),g("small",yu,"Edited by "+J(m.editor),1)],8,mu))),128))])):oe("",!0),!D(l)&&!i.value?(x(),O("div",bu,h[1]||(h[1]=[g("p",{class:"mb-0"},"Please select a class first to view and add documents.",-1)]))):D(l)&&D(r).length===0&&!i.value?(x(),O("div",_u,[g("p",wu,"No documents for "+J(D(l).name)+' yet. Click "+New Document" to get started.',1)])):oe("",!0)]))}},ku={class:"statistics-content"},Cu={key:0},Su={class:"table table-striped table-sm"},Au={class:"table-dark"},Du={key:1,class:"text-center text-muted py-4"},$u={key:2,class:"text-center text-muted py-4"},Eu={class:"mb-0"},ju={__name:"StatisticsContent",setup(e){const{filteredStatistics:t,selectedClass:n}=Kn();return(s,o)=>(x(),O("div",ku,[D(n)&&D(t).subjects.length>0?(x(),O("div",Cu,[g("table",Su,[g("thead",Au,[g("tr",null,[o[0]||(o[0]=g("th",{scope:"col"},null,-1)),(x(!0),O(ie,null,Me(D(t).subjects,r=>(x(),O("th",{scope:"col",key:r},J(r),1))),128))])]),g("tbody",null,[g("tr",null,[o[1]||(o[1]=g("th",{scope:"row",class:"text-muted"},"Class Average",-1)),(x(!0),O(ie,null,Me(D(t).classAverage,(r,l)=>(x(),O("td",{key:l},J(r),1))),128))]),g("tr",null,[o[2]||(o[2]=g("th",{scope:"row",class:"text-muted"},"Reached Norm",-1)),(x(!0),O(ie,null,Me(D(t).reachedNorm,(r,l)=>(x(),O("td",{key:l},[g("span",{class:Ee(r==="yes"?"badge bg-success":"badge bg-danger")},J(r),3)]))),128))]),g("tr",null,[o[3]||(o[3]=g("th",{scope:"row",class:"text-muted"},"Passing %",-1)),(x(!0),O(ie,null,Me(D(t).passingPerc,(r,l)=>(x(),O("td",{key:l},J(r),1))),128))])])])])):oe("",!0),D(n)?D(n)&&D(t).subjects.length===0?(x(),O("div",$u,[g("p",Eu,"No statistics data available for "+J(D(n).name)+" yet.",1)])):oe("",!0):(x(),O("div",Du,o[4]||(o[4]=[g("p",{class:"mb-0"},"Please select a class first to view statistics.",-1)])))]))}},Ou={key:4,class:"default-content"},Nu={__name:"QuadrantCard",props:{title:{type:String,default:"Quadrant Title"},addButtonText:{type:String,default:"Add Item"},type:{type:String,default:"default",validator:e=>["classes","students","documents","statistics","default"].includes(e)}},setup(e){const t=K(!1),n=()=>{t.value=!0},s=o=>{t.value=o};return(o,r)=>(x(),de(yt,{title:e.title},{"header-actions":ee(()=>[e.addButtonText&&e.type!=="statistics"?(x(),de(pe,{key:0,onClick:n,variant:"purple",size:"sm",icon:"bi-plus-lg"},{default:ee(()=>[me(J(e.addButtonText),1)]),_:1})):oe("",!0)]),default:ee(()=>[e.type==="classes"?(x(),de(Xc,{key:0,"show-add-form":t.value,onFormVisibilityChange:s},null,8,["show-add-form"])):e.type==="students"?(x(),de(iu,{key:1,"show-add-form":t.value,onFormVisibilityChange:s},null,8,["show-add-form"])):e.type==="documents"?(x(),de(xu,{key:2,"show-add-form":t.value,onFormVisibilityChange:s},null,8,["show-add-form"])):e.type==="statistics"?(x(),de(ju,{key:3})):(x(),O("div",Ou,[Rt(o.$slots,"default",{},()=>[r[0]||(r[0]=g("div",{class:"placeholder-content"},[g("p",{class:"placeholder-text"},"Add your content here")],-1))],!0)]))]),_:3},8,["title"]))}},Cn=ot(Nu,[["__scopeId","data-v-bcdc44ce"]]),Fu={class:"notification-header"},Mu={class:"d-flex gap-2"},Pu={class:"notification-body"},Tu={key:0,class:"text-center text-muted py-4"},Iu={key:1,class:"notification-list"},Ru=["onClick"],Vu={class:"notification-content"},Lu={class:"notification-message mb-1"},Uu={class:"text-muted"},Bu={class:"notification-actions"},qu={__name:"NotificationDialog",emits:["close","document-clicked"],setup(e,{emit:t}){const n=t,{sortedNotifications:s,unreadCount:o,markAsRead:r,markAllAsRead:l,removeNotification:a}=al(),i=()=>{n("close")},d=f=>{r(f.id),n("document-clicked",f.classId,f.documentId)},c=f=>{const h=new Date-f,m=Math.floor(h/(1e3*60)),A=Math.floor(h/(1e3*60*60)),C=Math.floor(h/(1e3*60*60*24));return m<1?"Just now":m<60?`${m}m ago`:A<24?`${A}h ago`:`${C}d ago`};return(f,h)=>(x(),O("div",{class:"notification-overlay",onClick:i},[g("div",{class:"notification-dialog",onClick:h[1]||(h[1]=mt(()=>{},["stop"]))},[g("div",Fu,[h[3]||(h[3]=g("h5",{class:"mb-0"},"Notifications",-1)),g("div",Mu,[D(o)>0?(x(),de(pe,{key:0,variant:"outline-secondary",size:"sm",onClick:D(l)},{default:ee(()=>h[2]||(h[2]=[me(" Mark all read ")])),_:1,__:[2]},8,["onClick"])):oe("",!0),z(pe,{variant:"outline-secondary",size:"sm",icon:"bi-x-lg",onClick:h[0]||(h[0]=m=>f.$emit("close"))})])]),g("div",Pu,[D(s).length===0?(x(),O("div",Tu,h[4]||(h[4]=[g("i",{class:"bi bi-bell-slash fs-1 mb-2"},null,-1),g("p",{class:"mb-0"},"No notifications",-1)]))):(x(),O("div",Iu,[(x(!0),O(ie,null,Me(D(s),m=>(x(),O("div",{key:m.id,class:Ee(["notification-item",{unread:!m.read}]),onClick:A=>d(m)},[g("div",Vu,[g("p",Lu,J(m.message),1),g("small",Uu,J(c(m.timestamp)),1)]),g("div",Bu,[z(pe,{variant:"outline-danger",size:"sm",icon:"bi-trash",onClick:mt(A=>D(a)(m.id),["stop"])},null,8,["onClick"])])],10,Ru))),128))]))])])]))}},zu=ot(qu,[["__scopeId","data-v-1ba576c0"]]);function il(){const e=K({id:1,firstName:"John",lastName:"Doe",role:"teaching_coach",email:"<EMAIL>"}),t=se(()=>e.value.role==="teaching_coach"),n=se(()=>e.value.role==="teacher"),s=se(()=>t.value),o=se(()=>!0),r=se(()=>t.value),l=se(()=>n.value),a=se(()=>!0),i=se(()=>!0);return{currentUser:e,isTeachingCoach:t,isTeacher:n,canCreateQuestions:s,canAnswerQuestions:o,canCreateActions:r,canCompleteActions:l,canWriteNotes:a,canCreateFloatingNotes:i,switchRole:d=>{e.value.role=d}}}const Qu={class:"role-switcher"},Ju={class:"dropdown"},Wu={class:"dropdown-menu"},Gu={class:"dropdown-item-text"},Ku={class:"text-muted"},Hu={__name:"RoleSwitcher",setup(e){const{currentUser:t,switchRole:n}=il();return(s,o)=>(x(),O("div",Qu,[g("div",Ju,[z(pe,{variant:"outline-light",size:"sm",class:"dropdown-toggle","data-bs-toggle":"dropdown","aria-expanded":"false",icon:"bi-person-gear"},{default:ee(()=>[me(J(D(t).role==="teaching_coach"?"Teaching Coach":"Teacher"),1)]),_:1}),g("ul",Wu,[g("li",null,[g("a",{class:Ee(["dropdown-item",{active:D(t).role==="teaching_coach"}]),href:"#",onClick:o[0]||(o[0]=mt(r=>D(n)("teaching_coach"),["prevent"]))},o[2]||(o[2]=[g("i",{class:"bi bi-mortarboard me-2"},null,-1),me(" Teaching Coach ")]),2)]),g("li",null,[g("a",{class:Ee(["dropdown-item",{active:D(t).role==="teacher"}]),href:"#",onClick:o[1]||(o[1]=mt(r=>D(n)("teacher"),["prevent"]))},o[3]||(o[3]=[g("i",{class:"bi bi-person me-2"},null,-1),me(" Teacher ")]),2)]),o[4]||(o[4]=g("li",null,[g("hr",{class:"dropdown-divider"})],-1)),g("li",null,[g("span",Gu,[g("small",Ku," Current: "+J(D(t).firstName)+" "+J(D(t).lastName),1)])])])])]))}},Xu=ot(Hu,[["__scopeId","data-v-35f5150f"]]),Yu={class:"app-container"},Zu={class:"navbar navbar-expand-lg navbar-dark bg-secondary"},ed={class:"container-fluid"},td={class:"navbar-brand"},nd={class:"d-flex align-items-center gap-2"},sd={key:0,class:"badge bg-danger ms-1"},od={key:1,class:"container-fluid py-4 bg-light min-vh-100"},rd={class:"row g-3"},ld={class:"col-md-6"},ad={class:"col-md-6"},id={class:"col-md-6"},cd={class:"col-md-6"},ud={__name:"App",setup(e){const t=qs(),n=K(null),s=K(!1),{unreadCount:o}=al();Qt("selectedClass",n),Qt("selectClass",l=>{n.value=l});const r=(l,a)=>{t.push(`/document/${l}/${a}`),s.value=!1};return(l,a)=>{const i=Hs("router-link"),d=Hs("router-view");return x(),O("div",Yu,[g("header",Zu,[g("div",ed,[g("div",td,[z(i,{to:"/",class:"text-decoration-none text-white"},{default:ee(()=>a[2]||(a[2]=[g("h1",{class:"h4 mb-0"},"topicus",-1)])),_:1,__:[2]})]),g("nav",nd,[z(pe,{variant:"outline-light",size:"sm",icon:"bi-bell","aria-label":"Notifications",onClick:a[0]||(a[0]=c=>s.value=!s.value)},{default:ee(()=>[D(o)>0?(x(),O("span",sd,J(D(o)),1)):oe("",!0)]),_:1}),z(Xu)])])]),l.$route.name==="DocumentView"?(x(),de(d,{key:0})):oe("",!0),!l.$route.name||l.$route.name!=="DocumentView"?(x(),O("main",od,[g("div",rd,[g("div",ld,[z(Cn,{title:"Classes","add-button-text":"Add Class",type:"classes"})]),g("div",ad,[z(Cn,{title:n.value?`Students - ${n.value.name}`:"Students - Select a class","add-button-text":"Add Student",type:"students"},null,8,["title"])]),g("div",id,[z(Cn,{title:n.value?`Documents - ${n.value.name}`:"Documents - Select a class","add-button-text":"Add Document",type:"documents"},null,8,["title"])]),g("div",cd,[z(Cn,{title:n.value?`Statistics - ${n.value.name}`:"Statistics - Select a class",type:"statistics"},null,8,["title"])])])])):oe("",!0),s.value?(x(),de(zu,{key:2,onClose:a[1]||(a[1]=c=>s.value=!1),onDocumentClicked:r})):oe("",!0)])}}};function dd(e,t){const n=K({id:t,title:"Class 5 May 2025",classId:e,lastEdited:new Date().toLocaleDateString("en-GB"),editor:"Mr. Smith"}),s=K([{id:1,question:"Is every student nice to each other?",answer:"yes",note:"Can you elaborate on this"},{id:2,question:"How are you controlling the class when they are too loud?",answer:"fix that",note:""},{id:3,question:"Did anything notable happen recently?",answer:"Jimmy is still eating his boogers",note:""}]),o=K([{id:1,subject:"Math",question:"Has the school norm been achieved?",answer:"yes",note:""},{id:2,subject:"Math",question:"Who is good at math?",answer:"not Jimmy",note:""},{id:3,subject:"Math",question:"Ho do you teach math?",answer:"book",note:""}]),r=K([{id:1,student:"Jimmy",question:"How is Jimmy's reading",answer:"yes",note:""},{id:2,student:"Jimmy",question:"Is Jimmy getting bullied",answer:"he should be",note:""},{id:3,student:"Jimmy",question:"Anything else?",answer:"nobody likes him",note:""}]),l=K(["Reading","Dutch","English","Math","PE"]),a=K(["brock","Ash","Pikachu","Jimmy","girl name"]),i=K("Math"),d=K("Jimmy"),c=K([{id:1,text:"Please be more specific",author:"Mr. Smith",timestamp:new Date}]),f=K([{id:1,text:"Pay more attention to Jimmy",completed:!0,createdBy:"teaching_coach"}]),h=K([]),m=se(()=>o.value.filter(C=>C.subject===i.value)),A=se(()=>r.value.filter(C=>C.student===d.value));return{document:n,overallQuestions:s,subjectQuestions:o,studentQuestions:r,subjects:l,students:a,selectedSubject:i,selectedStudent:d,generalNotes:c,actions:f,floatingNotes:h,filteredSubjectQuestions:m,filteredStudentQuestions:A,addOverallQuestion:C=>{const M={id:Date.now(),question:C,answer:"",note:""};return s.value.push(M),M},addSubjectQuestion:C=>{const M={id:Date.now(),subject:i.value,question:C,answer:"",note:""};return o.value.push(M),M},addStudentQuestion:C=>{const M={id:Date.now(),student:d.value,question:C,answer:"",note:""};return r.value.push(M),M},updateAnswer:(C,M,R)=>{let P;switch(C){case"overall":P=s.value;break;case"subject":P=o.value;break;case"student":P=r.value;break}const I=P.find(T=>T.id===M);I&&(I.answer=R)},addGeneralNote:C=>{const M={id:Date.now(),text:C,author:"Current User",timestamp:new Date};return c.value.push(M),M},addAction:C=>{const M={id:Date.now(),text:C,completed:!1,createdBy:"teaching_coach"};return f.value.push(M),M},toggleAction:C=>{const M=f.value.find(R=>R.id===C);M&&(M.completed=!M.completed)},addFloatingNote:(C,M,R,P)=>{const I={id:Date.now(),targetType:C,targetId:M,text:R,position:P,author:"Current User",timestamp:new Date};return h.value.push(I),I},saveDocument:async()=>{try{return console.log("Saving document...",{document:n.value,overallQuestions:s.value,subjectQuestions:o.value,studentQuestions:r.value,generalNotes:c.value,actions:f.value,floatingNotes:h.value}),n.value.lastEdited=new Date().toLocaleDateString("en-GB"),{success:!0,message:"Document saved successfully!"}}catch(C){return{success:!1,message:"Failed to save document: "+C.message}}}}}const fd={class:"d-flex justify-content-between align-items-center w-100"},pd={key:0,class:"dropdown"},hd=["value"],md=["value"],vd={class:"table-responsive"},gd={class:"table table-borderless"},yd=["onClick"],bd=["value","onInput","onClick"],_d=["onClick"],wd=["onClick"],xd={key:0,class:"mt-3"},kd={class:"input-group"},Cd={__name:"QuestionSection",props:{title:{type:String,required:!0},questions:{type:Array,default:()=>[]},canCreate:{type:Boolean,default:!1},canAnswer:{type:Boolean,default:!0},questionType:{type:String,required:!0},color:{type:String,default:"pink",validator:e=>["pink","purple"].includes(e)},showDropdown:{type:Boolean,default:!1},dropdownOptions:{type:Array,default:()=>[]},dropdownValue:{type:String,default:""},dropdownLabel:{type:String,default:"Selection"}},emits:["add-question","update-answer","add-floating-note","dropdown-change"],setup(e,{emit:t}){const n=e,s=t,o=K(""),r=()=>{o.value.trim()&&(s("add-question",o.value.trim()),o.value="")},l=(i,d)=>{s("update-answer",n.questionType,i,d)},a=(i,d,c)=>{const f=c.target.getBoundingClientRect(),h={x:f.left+window.scrollX,y:f.top+window.scrollY+f.height};console.log("Text clicked:",i,d,h),s("add-floating-note",i,d,"",h)};return(i,d)=>(x(),de(yt,{class:"mb-4"},{header:ee(()=>[g("div",fd,[g("h2",{class:Ee(["h5 mb-0 text-white",`bg-${e.color}`])},J(e.title),3),e.showDropdown?(x(),O("div",pd,[g("select",{value:e.dropdownValue,onChange:d[0]||(d[0]=c=>i.$emit("dropdown-change",c.target.value)),class:"form-select form-select-sm",style:{"min-width":"150px"}},[(x(!0),O(ie,null,Me(e.dropdownOptions,c=>(x(),O("option",{key:c,value:c},J(c),9,md))),128))],40,hd)])):oe("",!0)])]),default:ee(()=>[g("div",vd,[g("table",gd,[d[2]||(d[2]=g("thead",null,[g("tr",null,[g("th",{style:{width:"5%"}},"#"),g("th",{style:{width:"40%"}},"Question"),g("th",{style:{width:"40%"}},"Answer"),g("th",{style:{width:"15%"}},"Note")])],-1)),g("tbody",null,[(x(!0),O(ie,null,Me(e.questions,(c,f)=>(x(),O("tr",{key:c.id},[g("td",null,J(f+1),1),g("td",null,[g("div",{class:"question-text",onClick:h=>a("question",c.id,h)},J(c.question),9,yd)]),g("td",null,[e.canAnswer?(x(),O("input",{key:0,value:c.answer,onInput:h=>l(c.id,h.target.value),onClick:h=>a("answer",c.id,h),class:"form-control form-control-sm",placeholder:"Enter answer..."},null,40,bd)):(x(),O("div",{key:1,class:"answer-text",onClick:h=>a("answer",c.id,h)},J(c.answer||"No answer yet"),9,_d))]),g("td",null,[g("div",{class:"note-text",onClick:h=>a("note",c.id,h)},J(c.note||""),9,wd)])]))),128))])])]),e.canCreate?(x(),O("div",xd,[g("div",kd,[et(g("input",{"onUpdate:modelValue":d[1]||(d[1]=c=>o.value=c),onKeyup:mn(r,["enter"]),class:"form-control",placeholder:"Add new question..."},null,544),[[nt,o.value]]),z(pe,{onClick:r,variant:"purple",icon:"bi-plus-lg"},{default:ee(()=>d[3]||(d[3]=[me(" Add ")])),_:1,__:[3]})])])):oe("",!0)]),_:1}))}},us=ot(Cd,[["__scopeId","data-v-50fe4114"]]),Sd={class:"notes-list"},Ad={key:0,class:"text-center text-muted py-3"},Dd={key:1},$d={class:"note-content"},Ed={class:"note-text mb-1"},jd={class:"text-muted"},Od={key:0,class:"mt-3"},Nd={class:"mb-2"},Fd=["onKeyup"],Md={class:"d-flex justify-content-end"},Pd={__name:"GeneralNotes",props:{notes:{type:Array,default:()=>[]},canWrite:{type:Boolean,default:!0}},emits:["add-note"],setup(e,{emit:t}){const n=t,s=K(""),o=()=>{s.value.trim()&&(n("add-note",s.value.trim()),s.value="")},r=l=>{const a=new Date-l,i=Math.floor(a/(1e3*60)),d=Math.floor(a/(1e3*60*60)),c=Math.floor(a/(1e3*60*60*24));return i<1?"just now":i<60?`${i}m ago`:d<24?`${d}h ago`:c<7?`${c}d ago`:l.toLocaleDateString()};return(l,a)=>(x(),de(yt,{title:"General Notes",class:"mb-4"},{default:ee(()=>[g("div",Sd,[e.notes.length===0?(x(),O("div",Ad,a[1]||(a[1]=[g("i",{class:"bi bi-sticky fs-1 mb-2"},null,-1),g("p",{class:"mb-0"},"No notes yet",-1)]))):(x(),O("div",Dd,[(x(!0),O(ie,null,Me(e.notes,i=>(x(),O("div",{key:i.id,class:"note-item"},[g("div",$d,[g("p",Ed,J(i.text),1),g("small",jd," by "+J(i.author)+" • "+J(r(i.timestamp)),1)])]))),128))]))]),e.canWrite?(x(),O("div",Od,[g("div",Nd,[et(g("textarea",{"onUpdate:modelValue":a[0]||(a[0]=i=>s.value=i),onKeyup:mn(mt(o,["ctrl"]),["enter"]),class:"form-control",rows:"3",placeholder:"Add a general note... (Ctrl+Enter to save)"},null,40,Fd),[[nt,s.value]])]),g("div",Md,[z(pe,{onClick:o,variant:"purple",size:"sm",icon:"bi-plus-lg",disabled:!s.value.trim()},{default:ee(()=>a[2]||(a[2]=[me(" Add Note ")])),_:1,__:[2]},8,["disabled"])])])):oe("",!0)]),_:1}))}},Td=ot(Pd,[["__scopeId","data-v-4a0381cd"]]),Id={class:"actions-list"},Rd={key:0,class:"text-center text-muted py-3"},Vd={key:1},Ld={class:"form-check"},Ud=["id","checked","disabled","onChange"],Bd=["for"],qd={class:"action-meta"},zd={class:"text-muted"},Qd={key:0,class:"mt-3"},Jd={class:"mb-2"},Wd={class:"d-flex justify-content-end"},Gd={key:1,class:"mt-3"},Kd={__name:"ActionChecklist",props:{actions:{type:Array,default:()=>[]},canCreate:{type:Boolean,default:!1},canComplete:{type:Boolean,default:!0}},emits:["add-action","toggle-action"],setup(e,{emit:t}){const n=t,s=K(""),o=()=>{s.value.trim()&&(n("add-action",s.value.trim()),s.value="")},r=l=>{n("toggle-action",l)};return(l,a)=>(x(),de(yt,{title:"Actions",class:"mb-4"},{default:ee(()=>[g("div",Id,[e.actions.length===0?(x(),O("div",Rd,a[1]||(a[1]=[g("i",{class:"bi bi-list-check fs-1 mb-2"},null,-1),g("p",{class:"mb-0"},"No actions yet",-1)]))):(x(),O("div",Vd,[(x(!0),O(ie,null,Me(e.actions,i=>(x(),O("div",{key:i.id,class:"action-item"},[g("div",Ld,[g("input",{id:`action-${i.id}`,checked:i.completed,disabled:!e.canComplete&&!i.completed,onChange:d=>r(i.id),class:"form-check-input",type:"checkbox"},null,40,Ud),g("label",{for:`action-${i.id}`,class:Ee(["form-check-label",{"text-decoration-line-through text-muted":i.completed}])},J(i.text),11,Bd)]),g("div",qd,[g("small",zd," Created by "+J(i.createdBy==="teaching_coach"?"Teaching Coach":"Teacher"),1)])]))),128))]))]),e.canCreate?(x(),O("div",Qd,[g("div",Jd,[et(g("input",{"onUpdate:modelValue":a[0]||(a[0]=i=>s.value=i),onKeyup:mn(o,["enter"]),class:"form-control",placeholder:"Add new action item..."},null,544),[[nt,s.value]])]),g("div",Wd,[z(pe,{onClick:o,variant:"purple",size:"sm",icon:"bi-plus-lg",disabled:!s.value.trim()},{default:ee(()=>a[2]||(a[2]=[me(" Add Action ")])),_:1,__:[2]},8,["disabled"])])])):oe("",!0),!e.canCreate&&e.canComplete?(x(),O("div",Gd,a[3]||(a[3]=[g("small",{class:"text-muted"},[g("i",{class:"bi bi-info-circle me-1"}),me(" You can mark actions as complete but cannot create new ones. ")],-1)]))):oe("",!0)]),_:1}))}},Hd=ot(Kd,[["__scopeId","data-v-da8c8c9d"]]),Xd={class:"floating-note-header"},Yd={class:"text-muted"},Zd={class:"floating-note-actions"},ef={class:"floating-note-body"},tf=["onKeyup"],nf={key:1,class:"note-text mb-0"},sf={key:0,class:"floating-note-footer"},of={class:"d-flex justify-content-end gap-2"},rf={__name:"FloatingNote",props:{note:{type:Object,required:!0}},emits:["update","remove"],setup(e,{emit:t}){const n=e,s=t,o=K(!1),r=K(""),l=K(null),a=()=>{r.value=n.note.text,o.value=!0,Ts(()=>{l.value&&l.value.focus()})},i=()=>{r.value.trim()&&(s("update",n.note.id,r.value.trim()),o.value=!1)},d=()=>{r.value="",o.value=!1},c=f=>{const h=new Date-f,m=Math.floor(h/(1e3*60)),A=Math.floor(h/(1e3*60*60));return m<1?"just now":m<60?`${m}m ago`:A<24?`${A}h ago`:f.toLocaleDateString()};return(f,h)=>(x(),O("div",{class:Ee(["floating-note",{editing:o.value}])},[g("div",Xd,[g("small",Yd,J(e.note.author)+" • "+J(c(e.note.timestamp)),1),g("div",Zd,[o.value?oe("",!0):(x(),de(pe,{key:0,variant:"outline-secondary",size:"sm",icon:"bi-pencil",onClick:a})),z(pe,{variant:"outline-danger",size:"sm",icon:"bi-x-lg",onClick:h[0]||(h[0]=m=>f.$emit("remove"))})])]),g("div",ef,[o.value?et((x(),O("textarea",{key:0,"onUpdate:modelValue":h[1]||(h[1]=m=>r.value=m),onKeyup:[mn(mt(i,["ctrl"]),["enter"]),mn(d,["esc"])],class:"form-control form-control-sm",rows:"3",placeholder:"Enter note text...",ref_key:"textareaRef",ref:l},null,40,tf)),[[nt,r.value]]):(x(),O("p",nf,J(e.note.text),1))]),o.value?(x(),O("div",sf,[g("div",of,[z(pe,{variant:"secondary",size:"sm",onClick:d},{default:ee(()=>h[2]||(h[2]=[me(" Cancel ")])),_:1,__:[2]}),z(pe,{variant:"purple",size:"sm",onClick:i,disabled:!r.value.trim()},{default:ee(()=>h[3]||(h[3]=[me(" Save ")])),_:1,__:[3]},8,["disabled"])]),h[4]||(h[4]=g("small",{class:"text-muted"},"Ctrl+Enter to save, Esc to cancel",-1))])):oe("",!0),h[5]||(h[5]=g("div",{class:"connection-line"},null,-1))],2))}},lf=ot(rf,[["__scopeId","data-v-311d90dd"]]),af={class:"document-view"},cf={class:"document-header"},uf={class:"container-fluid"},df={class:"d-flex justify-content-between align-items-center"},ff={class:"d-flex align-items-center"},pf={class:"h4 mb-0"},hf={class:"d-flex align-items-center gap-2"},mf={class:"text-muted"},vf={class:"container-fluid py-4"},gf={class:"row g-4"},yf={class:"col-lg-8"},bf={class:"col-lg-4"},_f={__name:"DocumentView",props:{classId:{type:[String,Number],required:!0},documentId:{type:[String,Number],required:!0}},setup(e){const t=e,n=qs(),{document:s,overallQuestions:o,filteredSubjectQuestions:r,filteredStudentQuestions:l,subjects:a,students:i,selectedSubject:d,selectedStudent:c,generalNotes:f,actions:h,floatingNotes:m,addOverallQuestion:A,addSubjectQuestion:C,addStudentQuestion:M,updateAnswer:R,addGeneralNote:P,addAction:I,toggleAction:T,addFloatingNote:H,saveDocument:ge}=dd(t.classId,t.documentId),{canCreateQuestions:le,canAnswerQuestions:je,canCreateActions:Qe,canCompleteActions:Te,canWriteNotes:Je}=il(),Ie=K(!1),ye=K(null),At=()=>{n.push("/")},he=async()=>{Ie.value=!0,ye.value=null;try{const te=await ge();ye.value={type:te.success?"success":"error",text:te.message},setTimeout(()=>{ye.value=null},3e3)}catch{ye.value={type:"error",text:"An unexpected error occurred while saving."}}finally{Ie.value=!1}};return xr(()=>{console.log("Document view mounted for class:",t.classId,"document:",t.documentId)}),(te,W)=>(x(),O("div",af,[g("header",cf,[g("div",uf,[g("div",df,[g("div",ff,[z(pe,{variant:"outline-secondary",size:"sm",icon:"bi-arrow-left",onClick:At,class:"me-3"},{default:ee(()=>W[2]||(W[2]=[me(" Back ")])),_:1,__:[2]}),g("h1",pf,J(D(s).title),1)]),g("div",hf,[g("small",mf,"Last edited: "+J(D(s).lastEdited),1),z(pe,{variant:"purple",icon:"bi-save",onClick:he,disabled:Ie.value},{default:ee(()=>[me(J(Ie.value?"Saving...":"Save"),1)]),_:1},8,["disabled"])])])])]),g("main",vf,[g("div",gf,[g("div",yf,[z(us,{title:"Overall class questions",questions:D(o),"can-create":D(le),"can-answer":D(je),"question-type":"overall",color:"pink",onAddQuestion:D(A),onUpdateAnswer:D(R),onAddFloatingNote:D(H)},null,8,["questions","can-create","can-answer","onAddQuestion","onUpdateAnswer","onAddFloatingNote"]),z(us,{title:`${D(d)}`,questions:D(r),"can-create":D(le),"can-answer":D(je),"question-type":"subject",color:"purple","show-dropdown":!0,"dropdown-options":D(a),"dropdown-value":D(d),"dropdown-label":"Subject Selection",onDropdownChange:W[0]||(W[0]=fe=>d.value=fe),onAddQuestion:D(C),onUpdateAnswer:D(R),onAddFloatingNote:D(H)},null,8,["title","questions","can-create","can-answer","dropdown-options","dropdown-value","onAddQuestion","onUpdateAnswer","onAddFloatingNote"]),z(us,{title:`${D(c)} Studentname`,questions:D(l),"can-create":D(le),"can-answer":D(je),"question-type":"student",color:"pink","show-dropdown":!0,"dropdown-options":D(i),"dropdown-value":D(c),"dropdown-label":"Student Selection",onDropdownChange:W[1]||(W[1]=fe=>c.value=fe),onAddQuestion:D(M),onUpdateAnswer:D(R),onAddFloatingNote:D(H)},null,8,["title","questions","can-create","can-answer","dropdown-options","dropdown-value","onAddQuestion","onUpdateAnswer","onAddFloatingNote"])]),g("div",bf,[z(Td,{notes:D(f),"can-write":D(Je),onAddNote:D(P)},null,8,["notes","can-write","onAddNote"]),z(Hd,{actions:D(h),"can-create":D(Qe),"can-complete":D(Te),onAddAction:D(I),onToggleAction:D(T)},null,8,["actions","can-create","can-complete","onAddAction","onToggleAction"])])])]),(x(!0),O(ie,null,Me(D(m),fe=>(x(),de(lf,{key:fe.id,note:fe,style:Ln({top:fe.position.y+"px",left:fe.position.x+"px"})},null,8,["note","style"]))),128)),ye.value?(x(),O("div",{key:0,class:Ee(["alert",ye.value.type==="success"?"alert-success":"alert-danger","save-message"])},J(ye.value.text),3)):oe("",!0)]))}},wf=ot(_f,[["__scopeId","data-v-202b7744"]]),xf=[{path:"/document/:classId/:documentId",name:"DocumentView",component:wf,props:!0}],kf=Oc({history:lc("/Topicus/"),routes:xf}),cl=Di(ud);cl.use(kf);cl.mount("#app");
