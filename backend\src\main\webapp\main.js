import 'bootstrap/dist/css/bootstrap.min.css'
import 'bootstrap-icons/font/bootstrap-icons.css'
import './assets/main.css'

import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import DocumentView from './components/DocumentView.vue'

// Define routes
const routes = [
  {
    path: '/document/:classId/:documentId',
    name: 'DocumentView',
    component: DocumentView,
    props: true
  }
]

// Create router
const router = createRouter({
  history: createWebHistory('/Topicus/'),
  routes
})

const app = createApp(App)
app.use(router)
app.mount('#app')
