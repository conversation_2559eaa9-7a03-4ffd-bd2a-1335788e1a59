import { ref, reactive, computed, inject } from 'vue'

export function useClassData() {
  // Inject global state
  const selectedClass = inject('selectedClass')
  const selectClass = inject('selectClass')

  // Reactive data arrays
  const classesData = ref([])
  const studentsData = ref([])
  const documentsData = ref([])

  // Statistics data
  const statisticsData = reactive({
    subjects: ['math', 'english', 'science'],
    classAverage: [8.2, 7.5, 8.8],
    reachedNorm: ['yes', 'no', 'yes'],
    passingPerc: ['85%', '72%', '91%']
  })

  // Computed properties for filtered data
  const filteredStudents = computed(() => {
    if (!selectedClass.value) return []
    return studentsData.value.filter(student => student.classId === selectedClass.value.id)
  })

  const filteredDocuments = computed(() => {
    if (!selectedClass.value) return []
    return documentsData.value.filter(document => document.classId === selectedClass.value.id)
  })

  const filteredStatistics = computed(() => {
    if (!selectedClass.value) return { subjects: [], classAverage: [], reachedNorm: [], passingPerc: [] }
    return statisticsData
  })

  // CRUD operations
  const addClass = (classData) => {
    const newClass = {
      id: Date.now(),
      name: classData.name,
      teacher: `${classData.teacherFirstName} ${classData.teacherLastName}`
    }
    classesData.value.push(newClass)
    return newClass
  }

  const addStudent = (studentName) => {
    if (!selectedClass.value) return null
    const newStudent = {
      id: Date.now(),
      name: studentName.trim(),
      classId: selectedClass.value.id
    }
    studentsData.value.push(newStudent)
    return newStudent
  }

  const addDocument = (documentData) => {
    if (!selectedClass.value) return null
    const newDocument = {
      id: Date.now(),
      title: documentData.title,
      lastEdited: new Date().toLocaleDateString('en-GB'),
      editor: documentData.editor,
      classId: selectedClass.value.id
    }
    documentsData.value.push(newDocument)
    return newDocument
  }

  // Initialize with some sample data
  const initializeSampleData = () => {
    // Sample classes
    classesData.value = [
      { id: 1, name: 'Class 5', teacher: 'Mr. Smith' },
      { id: 2, name: 'Class 6', teacher: 'Ms. Johnson' }
    ]

    // Sample students
    studentsData.value = [
      { id: 1, name: 'Jimmy Studentname', classId: 1 },
      { id: 2, name: 'Ash Ketchum', classId: 1 },
      { id: 3, name: 'Pikachu', classId: 1 },
      { id: 4, name: 'Brock Harrison', classId: 1 },
      { id: 5, name: 'Girl Name', classId: 1 },
      { id: 6, name: 'John Doe', classId: 2 },
      { id: 7, name: 'Jane Smith', classId: 2 }
    ]

    // Sample documents
    documentsData.value = [
      { id: 1, title: 'May 2025', lastEdited: '15/01/2025', editor: 'Mr. Smith', classId: 1 },
      { id: 2, title: 'April 2025', lastEdited: '10/01/2025', editor: 'Mr. Smith', classId: 1 },
      { id: 3, title: 'June 2025', lastEdited: '12/01/2025', editor: 'Ms. Johnson', classId: 2 }
    ]
  }

  // Initialize sample data on first load
  if (classesData.value.length === 0) {
    initializeSampleData()
  }

  const handleClassSelect = (classItem) => {
    selectClass(classItem)
  }

  return {
    // Data
    classesData,
    studentsData,
    documentsData,
    statisticsData,
    selectedClass,

    // Computed
    filteredStudents,
    filteredDocuments,
    filteredStatistics,

    // Methods
    addClass,
    addStudent,
    addDocument,
    handleClassSelect
  }
}
