# Topicus Dashboard - Tomcat Deployment Script
# PowerShell version

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Topicus Dashboard - Tomcat Deployment" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Check if CATALINA_HOME is set
if (-not $env:CATALINA_HOME) {
    Write-Host "ERROR: CATALINA_HOME environment variable is not set!" -ForegroundColor Red
    Write-Host "Please set CATALINA_HOME to your Tomcat installation directory." -ForegroundColor Yellow
    Write-Host "Example: `$env:CATALINA_HOME = 'C:\apache-tomcat-9.0.xx'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "CATALINA_HOME: $env:CATALINA_HOME" -ForegroundColor Green
Write-Host ""

# Navigate to webapp directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$webappPath = Join-Path $scriptPath "webapp"

if (-not (Test-Path $webappPath)) {
    Write-Host "ERROR: Could not find webapp directory at $webappPath" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Set-Location $webappPath

# Build Vue.js application
Write-Host "Building Vue.js application..." -ForegroundColor Yellow
try {
    & npm run build
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
    Write-Host "Build completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Build failed!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Stop Tomcat
Write-Host "Stopping Tomcat..." -ForegroundColor Yellow
try {
    & "$env:CATALINA_HOME\bin\shutdown.bat"
    Start-Sleep -Seconds 5
} catch {
    Write-Host "Warning: Could not stop Tomcat gracefully" -ForegroundColor Yellow
}

# Remove existing deployment
$tomcatWebapps = Join-Path $env:CATALINA_HOME "webapps\Topicus"
if (Test-Path $tomcatWebapps) {
    Write-Host "Removing existing Topicus deployment..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force $tomcatWebapps
}

# Copy new deployment
Write-Host "Deploying to Tomcat..." -ForegroundColor Yellow
try {
    $sourcePath = Join-Path $webappPath "Topicus"
    Copy-Item -Recurse -Force $sourcePath $tomcatWebapps
    Write-Host "Deployment completed successfully!" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Deployment failed!" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Start Tomcat
Write-Host "Starting Tomcat..." -ForegroundColor Yellow
try {
    & "$env:CATALINA_HOME\bin\startup.bat"
} catch {
    Write-Host "ERROR: Could not start Tomcat!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Deployment Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "The application will be available at:" -ForegroundColor Yellow
Write-Host "http://localhost:8080/Topicus/" -ForegroundColor Cyan
Write-Host ""
Write-Host "Wait a few seconds for Tomcat to start, then open the URL in your browser." -ForegroundColor Yellow
Write-Host ""

# Optional: Open browser automatically
$openBrowser = Read-Host "Would you like to open the application in your browser now? (y/n)"
if ($openBrowser -eq "y" -or $openBrowser -eq "Y") {
    Start-Sleep -Seconds 3
    Start-Process "http://localhost:8080/Topicus/"
}

Read-Host "Press Enter to exit"
