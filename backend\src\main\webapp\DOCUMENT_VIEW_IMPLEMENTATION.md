# Document Viewing/Editing Page Implementation

## Overview

This implementation creates a comprehensive document viewing and editing system for the Vue.js frontend application. The system allows users to view and interact with class documents based on their role permissions.

## Features Implemented

### 1. Navigation System
- **Vue Router Integration**: Added routing to navigate between home page and document pages
- **Clickable Documents**: Documents in DocumentsContent.vue are now clickable and navigate to specific document pages
- **Back Navigation**: Document pages include a back button to return to the home page

### 2. Document View Page Structure
The document page (`/document/:classId/:documentId`) includes:

#### Three Main Question Sections:
1. **Overall Class Questions** (Pink header)
   - Can only be created by teaching coaches
   - Both roles can answer questions
   
2. **Subject-Specific Questions** (Purple header)
   - Dynamic subject dropdown selection
   - Questions filtered by selected subject
   - Can only be created by teaching coaches
   
3. **Individual Student Questions** (Pink header)
   - Dynamic student dropdown selection
   - Questions filtered by selected student
   - Can only be created by teaching coaches

#### Right Sidebar:
1. **General Notes Section**
   - Both teaching coaches and teachers can write notes
   - Timestamped notes with author information
   
2. **Actions Section**
   - Teaching coaches can create action items
   - Teachers can mark actions as complete
   - Checkbox-based interface

### 3. Role-Based Permissions
- **Teaching Coach**: Can create questions, create actions, write notes, answer questions
- **Teacher**: Can only answer questions, complete actions, write notes (cannot create questions or actions)

### 4. Interactive Features
- **Subject Dropdown**: Updates displayed subject questions dynamically
- **Student Dropdown**: Updates displayed student questions dynamically
- **Floating Notes**: Click on any question/answer text to create floating notes (framework implemented)
- **Save Functionality**: Save button persists all changes

### 5. Notifications System
- **Notification Dialog**: Floating dialog accessible from header bell icon
- **Document Navigation**: Click notifications to navigate to specific documents
- **Real-time Updates**: Shows when documents are updated

## File Structure

### New Components Created:
```
components/
├── DocumentView.vue              # Main document page
├── QuestionSection.vue           # Reusable question section component
├── GeneralNotes.vue             # General notes sidebar component
├── ActionChecklist.vue          # Actions/checklist sidebar component
├── FloatingNote.vue             # Floating notes attached to text
└── NotificationDialog.vue       # Notifications popup dialog
```

### New Composables:
```
composables/
├── useDocumentData.js           # Document-specific data management
├── useUserRole.js               # Role-based permissions system
└── useNotifications.js          # Notifications management
```

### New Services:
```
services/
└── documentApi.js               # API calls for document operations
```

## Technical Implementation Details

### 1. Routing Configuration
- Updated `main.js` to include Vue Router
- Added route for `/document/:classId/:documentId`
- Modified `App.vue` to conditionally show router-view or home content

### 2. State Management
- **useDocumentData**: Manages all document-related state (questions, notes, actions)
- **useUserRole**: Handles role-based permissions and user information
- **useNotifications**: Manages notification state and operations
- **useClassData**: Extended with sample data for testing

### 3. Component Architecture
- **Modular Design**: Each section is a separate reusable component
- **Props-based Communication**: Components communicate via props and events
- **Composable Integration**: Components use composables for state management

### 4. Styling
- **Bootstrap Integration**: Uses existing Bootstrap classes
- **Custom Colors**: Pink (#E8436E) and Purple (#872650) theme colors
- **Responsive Design**: Works on different screen sizes
- **Interactive Elements**: Hover effects and transitions

## Data Structure

### Document Data Model:
```javascript
{
  id: number,
  title: string,
  classId: number,
  lastEdited: string,
  editor: string
}
```

### Question Data Model:
```javascript
{
  id: number,
  question: string,
  answer: string,
  note: string,
  subject?: string,    // for subject questions
  student?: string     // for student questions
}
```

### Action Data Model:
```javascript
{
  id: number,
  text: string,
  completed: boolean,
  createdBy: string
}
```

## Backend Integration

### API Endpoints Expected:
- `GET /api/documents/:classId/:documentId` - Get document data
- `PUT /api/documents/:classId/:documentId` - Save document
- `POST /api/documents/:classId/:documentId/questions` - Add question
- `PUT /api/documents/:classId/:documentId/questions/:questionId/answer` - Update answer
- `POST /api/documents/:classId/:documentId/notes` - Add note
- `POST /api/documents/:classId/:documentId/actions` - Add action
- `PUT /api/documents/:classId/:documentId/actions/:actionId` - Update action status

### Database Integration:
The implementation is designed to work with the existing `DBConnection.java` backend. The API service layer (`documentApi.js`) provides the interface between the frontend and backend.

## Usage Instructions

### For Teaching Coaches:
1. Click on any document from the home page
2. Create questions in any of the three sections
3. Answer existing questions
4. Create action items for teachers
5. Write general notes
6. Use subject/student dropdowns to filter questions
7. Click Save to persist changes

### For Teachers:
1. Click on any document from the home page
2. Answer questions (cannot create new ones)
3. Mark action items as complete
4. Write general notes
5. Use subject/student dropdowns to view different questions
6. Click Save to persist changes

## Future Enhancements

1. **Real Floating Notes**: Complete implementation of floating notes with drag-and-drop
2. **Rich Text Editor**: Add formatting options for notes and answers
3. **File Attachments**: Allow attaching files to notes or questions
4. **Real-time Collaboration**: WebSocket integration for live updates
5. **Export Functionality**: Export documents to PDF or other formats
6. **Search and Filter**: Advanced search across questions and notes
7. **Version History**: Track changes and allow reverting to previous versions

## Testing

The implementation includes sample data for testing:
- 2 sample classes with students and documents
- Pre-populated questions in all three categories
- Sample notifications
- Role switching functionality for testing permissions

To test the application:
1. Run `npm run dev` in the webapp directory
2. Navigate to `http://localhost:5173/Topicus/`
3. Select a class and click on a document
4. Test different user roles using the role switching function in `useUserRole.js`
