<template>
  <div class="role-switcher">
    <div class="dropdown">
      <BaseButton
        variant="outline-light"
        size="sm"
        class="dropdown-toggle"
        data-bs-toggle="dropdown"
        aria-expanded="false"
        icon="bi-person-gear"
      >
        {{ currentUser.role === 'teaching_coach' ? 'Teaching Coach' : 'Teacher' }}
      </BaseButton>
      <ul class="dropdown-menu">
        <li>
          <a 
            class="dropdown-item" 
            href="#"
            :class="{ active: currentUser.role === 'teaching_coach' }"
            @click.prevent="switchRole('teaching_coach')"
          >
            <i class="bi bi-mortarboard me-2"></i>
            Teaching Coach
          </a>
        </li>
        <li>
          <a 
            class="dropdown-item" 
            href="#"
            :class="{ active: currentUser.role === 'teacher' }"
            @click.prevent="switchRole('teacher')"
          >
            <i class="bi bi-person me-2"></i>
            Teacher
          </a>
        </li>
        <li><hr class="dropdown-divider"></li>
        <li>
          <span class="dropdown-item-text">
            <small class="text-muted">
              Current: {{ currentUser.firstName }} {{ currentUser.lastName }}
            </small>
          </span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { useUserRole } from '../composables/useUserRole.js'
import BaseButton from './ui/BaseButton.vue'

const { currentUser, switchRole } = useUserRole()
</script>

<style scoped>
.role-switcher {
  position: relative;
}

.dropdown-item.active {
  background-color: #E8436E;
  color: white;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item.active:hover {
  background-color: #d63384;
}
</style>
