<template>
  <div class="notification-overlay" @click="handleOverlayClick">
    <div class="notification-dialog" @click.stop>
      <div class="notification-header">
        <h5 class="mb-0">Notifications</h5>
        <div class="d-flex gap-2">
          <BaseButton
            v-if="unreadCount > 0"
            variant="outline-secondary"
            size="sm"
            @click="markAllAsRead"
          >
            Mark all read
          </BaseButton>
          <BaseButton
            variant="outline-secondary"
            size="sm"
            icon="bi-x-lg"
            @click="$emit('close')"
          />
        </div>
      </div>
      
      <div class="notification-body">
        <div v-if="sortedNotifications.length === 0" class="text-center text-muted py-4">
          <i class="bi bi-bell-slash fs-1 mb-2"></i>
          <p class="mb-0">No notifications</p>
        </div>
        
        <div v-else class="notification-list">
          <div
            v-for="notification in sortedNotifications"
            :key="notification.id"
            :class="[
              'notification-item',
              { 'unread': !notification.read }
            ]"
            @click="handleNotificationClick(notification)"
          >
            <div class="notification-content">
              <p class="notification-message mb-1">{{ notification.message }}</p>
              <small class="text-muted">{{ formatTimestamp(notification.timestamp) }}</small>
            </div>
            <div class="notification-actions">
              <BaseButton
                variant="outline-danger"
                size="sm"
                icon="bi-trash"
                @click.stop="removeNotification(notification.id)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useNotifications } from '../composables/useNotifications.js'
import BaseButton from './ui/BaseButton.vue'

const emit = defineEmits(['close', 'document-clicked'])

const { 
  sortedNotifications, 
  unreadCount, 
  markAsRead, 
  markAllAsRead, 
  removeNotification 
} = useNotifications()

const handleOverlayClick = () => {
  emit('close')
}

const handleNotificationClick = (notification) => {
  markAsRead(notification.id)
  emit('document-clicked', notification.classId, notification.documentId)
}

const formatTimestamp = (timestamp) => {
  const now = new Date()
  const diff = now - timestamp
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return 'Just now'
  if (minutes < 60) return `${minutes}m ago`
  if (hours < 24) return `${hours}h ago`
  return `${days}d ago`
}
</script>

<style scoped>
.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 80px;
  z-index: 1050;
}

.notification-dialog {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.notification-header {
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-body {
  flex: 1;
  overflow-y: auto;
  max-height: 60vh;
}

.notification-list {
  padding: 0;
}

.notification-item {
  padding: 1rem;
  border-bottom: 1px solid #f8f9fa;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.notification-content {
  flex: 1;
}

.notification-message {
  font-weight: 500;
  color: #333;
}

.notification-actions {
  margin-left: 1rem;
}
</style>
