<script setup>
import { ref, provide } from 'vue'
import { useRouter } from 'vue-router'
import { useNotifications } from './composables/useNotifications.js'
import QuadrantCard from './components/QuadrantCard.vue'
import BaseButton from './components/ui/BaseButton.vue'
import NotificationDialog from './components/NotificationDialog.vue'
import RoleSwitcher from './components/RoleSwitcher.vue'

const router = useRouter()

// Global state for selected class
const selectedClass = ref(null)
const showNotifications = ref(false)

// Notifications
const { unreadCount } = useNotifications()

// Provide the selected class to all child components
provide('selectedClass', selectedClass)

// Function to handle class selection
const selectClass = (classData) => {
  selectedClass.value = classData
}

// Provide the select function to child components
provide('selectClass', selectClass)

// Handle notification document click
const handleNotificationDocumentClick = (classId, documentId) => {
  router.push(`/document/${classId}/${documentId}`)
  showNotifications.value = false
}
</script>

<template>
  <div class="app-container">
    <!-- Top Navigation Bar -->
    <header class="navbar navbar-expand-lg navbar-dark bg-secondary">
      <div class="container-fluid">
        <div class="navbar-brand">
          <router-link to="/" class="text-decoration-none text-white">
            <h1 class="h4 mb-0">topicus</h1>
          </router-link>
        </div>
        <nav class="d-flex align-items-center gap-2">
          <BaseButton
            variant="outline-light"
            size="sm"
            icon="bi-bell"
            aria-label="Notifications"
            @click="showNotifications = !showNotifications"
          >
            <span v-if="unreadCount > 0" class="badge bg-danger ms-1">{{ unreadCount }}</span>
          </BaseButton>
          <RoleSwitcher />
        </nav>
      </div>
    </header>

    <!-- Router View for Different Pages -->
    <router-view v-if="$route.name === 'DocumentView'" />

    <!-- Main Content Area with 4 Quadrants (Home Page) -->
    <main v-if="!$route.name || $route.name !== 'DocumentView'" class="container-fluid py-4 bg-light min-vh-100">
      <div class="row g-3">
        <div class="col-md-6">
          <QuadrantCard
            title="Classes"
            add-button-text="Add Class"
            type="classes"
          />
        </div>
        <div class="col-md-6">
          <QuadrantCard
            :title="selectedClass ? `Students - ${selectedClass.name}` : 'Students - Select a class'"
            add-button-text="Add Student"
            type="students"
          />
        </div>
        <div class="col-md-6">
          <QuadrantCard
            :title="selectedClass ? `Documents - ${selectedClass.name}` : 'Documents - Select a class'"
            add-button-text="Add Document"
            type="documents"
          />
        </div>
        <div class="col-md-6">
          <QuadrantCard
            :title="selectedClass ? `Statistics - ${selectedClass.name}` : 'Statistics - Select a class'"
            type="statistics"
          />
        </div>
      </div>
    </main>

    <!-- Notifications Dialog -->
    <NotificationDialog
      v-if="showNotifications"
      @close="showNotifications = false"
      @document-clicked="handleNotificationDocumentClick"
    />
  </div>
</template>