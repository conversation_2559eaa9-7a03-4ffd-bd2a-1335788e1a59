// API service for document-related operations

const API_URL = '/api';

// Document API
export async function getDocument(classId, documentId) {
  try {
    const response = await fetch(`${API_URL}/documents/${classId}/${documentId}`);
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching document:', error);
    throw error;
  }
}

export async function saveDocument(classId, documentId, documentData) {
  try {
    const response = await fetch(`${API_URL}/documents/${classId}/${documentId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(documentData),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! Status: ${response.status}, Message: ${errorText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error saving document:', error);
    throw error;
  }
}

// Questions API
export async function addQuestion(classId, documentId, questionType, questionData) {
  try {
    const response = await fetch(`${API_URL}/documents/${classId}/${documentId}/questions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: questionType,
        ...questionData
      }),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! Status: ${response.status}, Message: ${errorText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error adding question:', error);
    throw error;
  }
}

export async function updateAnswer(classId, documentId, questionId, answer) {
  try {
    const response = await fetch(`${API_URL}/documents/${classId}/${documentId}/questions/${questionId}/answer`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ answer }),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! Status: ${response.status}, Message: ${errorText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error updating answer:', error);
    throw error;
  }
}

// Notes API
export async function addNote(classId, documentId, noteData) {
  try {
    const response = await fetch(`${API_URL}/documents/${classId}/${documentId}/notes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(noteData),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! Status: ${response.status}, Message: ${errorText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error adding note:', error);
    throw error;
  }
}

// Actions API
export async function addAction(classId, documentId, actionData) {
  try {
    const response = await fetch(`${API_URL}/documents/${classId}/${documentId}/actions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(actionData),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! Status: ${response.status}, Message: ${errorText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error adding action:', error);
    throw error;
  }
}

export async function updateActionStatus(classId, documentId, actionId, completed) {
  try {
    const response = await fetch(`${API_URL}/documents/${classId}/${documentId}/actions/${actionId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ completed }),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! Status: ${response.status}, Message: ${errorText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error updating action status:', error);
    throw error;
  }
}

// Floating Notes API
export async function addFloatingNote(classId, documentId, floatingNoteData) {
  try {
    const response = await fetch(`${API_URL}/documents/${classId}/${documentId}/floating-notes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(floatingNoteData),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! Status: ${response.status}, Message: ${errorText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error adding floating note:', error);
    throw error;
  }
}

// Subjects and Students API
export async function getSubjectsForClass(classId) {
  try {
    const response = await fetch(`${API_URL}/classes/${classId}/subjects`);
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching subjects:', error);
    throw error;
  }
}

export async function getStudentsForClass(classId) {
  try {
    const response = await fetch(`${API_URL}/classes/${classId}/students`);
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching students:', error);
    throw error;
  }
}
