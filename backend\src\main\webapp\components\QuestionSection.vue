<template>
  <BaseCard class="mb-4">
    <template #header>
      <div class="d-flex justify-content-between align-items-center w-100">
        <h2 :class="['h5 mb-0 text-white', `bg-${color}`]">{{ title }}</h2>
        <div v-if="showDropdown" class="dropdown">
          <select 
            :value="dropdownValue" 
            @change="$emit('dropdown-change', $event.target.value)"
            class="form-select form-select-sm"
            style="min-width: 150px;"
          >
            <option v-for="option in dropdownOptions" :key="option" :value="option">
              {{ option }}
            </option>
          </select>
        </div>
      </div>
    </template>

    <!-- Questions Table -->
    <div class="table-responsive">
      <table class="table table-borderless">
        <thead>
          <tr>
            <th style="width: 5%">#</th>
            <th style="width: 40%">Question</th>
            <th style="width: 40%">Answer</th>
            <th style="width: 15%">Note</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(question, index) in questions" :key="question.id">
            <td>{{ index + 1 }}</td>
            <td>
              <div 
                class="question-text"
                @click="handleTextClick('question', question.id, $event)"
              >
                {{ question.question }}
              </div>
            </td>
            <td>
              <input
                v-if="canAnswer"
                :value="question.answer"
                @input="updateQuestionAnswer(question.id, $event.target.value)"
                @click="handleTextClick('answer', question.id, $event)"
                class="form-control form-control-sm"
                placeholder="Enter answer..."
              />
              <div 
                v-else
                class="answer-text"
                @click="handleTextClick('answer', question.id, $event)"
              >
                {{ question.answer || 'No answer yet' }}
              </div>
            </td>
            <td>
              <div 
                class="note-text"
                @click="handleTextClick('note', question.id, $event)"
              >
                {{ question.note || '' }}
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Add Question Input -->
    <div v-if="canCreate" class="mt-3">
      <div class="input-group">
        <input
          v-model="newQuestionText"
          @keyup.enter="handleAddQuestion"
          class="form-control"
          placeholder="Add new question..."
        />
        <BaseButton
          @click="handleAddQuestion"
          variant="purple"
          icon="bi-plus-lg"
        >
          Add
        </BaseButton>
      </div>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref } from 'vue'
import BaseCard from './ui/BaseCard.vue'
import BaseButton from './ui/BaseButton.vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  questions: {
    type: Array,
    default: () => []
  },
  canCreate: {
    type: Boolean,
    default: false
  },
  canAnswer: {
    type: Boolean,
    default: true
  },
  questionType: {
    type: String,
    required: true
  },
  color: {
    type: String,
    default: 'pink',
    validator: (value) => ['pink', 'purple'].includes(value)
  },
  showDropdown: {
    type: Boolean,
    default: false
  },
  dropdownOptions: {
    type: Array,
    default: () => []
  },
  dropdownValue: {
    type: String,
    default: ''
  },
  dropdownLabel: {
    type: String,
    default: 'Selection'
  }
})

const emit = defineEmits([
  'add-question',
  'update-answer',
  'add-floating-note',
  'dropdown-change'
])

const newQuestionText = ref('')

const handleAddQuestion = () => {
  if (newQuestionText.value.trim()) {
    emit('add-question', newQuestionText.value.trim())
    newQuestionText.value = ''
  }
}

const updateQuestionAnswer = (questionId, answer) => {
  emit('update-answer', props.questionType, questionId, answer)
}

const handleTextClick = (textType, questionId, event) => {
  // Get click position for floating note
  const rect = event.target.getBoundingClientRect()
  const position = {
    x: rect.left + window.scrollX,
    y: rect.top + window.scrollY + rect.height
  }
  
  // For now, just log the click - in a real implementation,
  // this would show a floating note creation dialog
  console.log('Text clicked:', textType, questionId, position)
  
  // Emit event for floating note creation
  emit('add-floating-note', textType, questionId, '', position)
}
</script>

<style scoped>
.bg-pink {
  background-color: #E8436E !important;
}

.bg-purple {
  background-color: #872650 !important;
}

.question-text,
.answer-text,
.note-text {
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  min-height: 1.5rem;
}

.question-text:hover,
.answer-text:hover,
.note-text:hover {
  background-color: #f8f9fa;
}

.table th {
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
}

.table td {
  vertical-align: middle;
  border-bottom: 1px solid #f8f9fa;
}

.form-control-sm {
  font-size: 0.875rem;
}
</style>
