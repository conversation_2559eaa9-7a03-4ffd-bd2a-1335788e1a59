<template>
  <div class="document-view">
    <!-- Header -->
    <header class="document-header">
      <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center">
          <div class="d-flex align-items-center">
            <BaseButton
              variant="outline-secondary"
              size="sm"
              icon="bi-arrow-left"
              @click="goBack"
              class="me-3"
            >
              Back
            </BaseButton>
            <h1 class="h4 mb-0">{{ document.title }}</h1>
          </div>
          <div class="d-flex align-items-center gap-2">
            <small class="text-muted">Last edited: {{ document.lastEdited }}</small>
            <BaseButton
              variant="purple"
              icon="bi-save"
              @click="handleSave"
              :disabled="isSaving"
            >
              {{ isSaving ? 'Saving...' : 'Save' }}
            </BaseButton>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="container-fluid py-4">
      <div class="row g-4">
        <!-- Left Column - Questions -->
        <div class="col-lg-8">
          <!-- Overall Class Questions -->
          <QuestionSection
            title="Overall class questions"
            :questions="overallQuestions"
            :can-create="canCreateQuestions"
            :can-answer="canAnswerQuestions"
            question-type="overall"
            color="pink"
            @add-question="addOverallQuestion"
            @update-answer="updateAnswer"
            @add-floating-note="addFloatingNote"
          />

          <!-- Subject Questions -->
          <QuestionSection
            :title="`${selectedSubject}`"
            :questions="filteredSubjectQuestions"
            :can-create="canCreateQuestions"
            :can-answer="canAnswerQuestions"
            question-type="subject"
            color="purple"
            :show-dropdown="true"
            :dropdown-options="subjects"
            :dropdown-value="selectedSubject"
            dropdown-label="Subject Selection"
            @dropdown-change="selectedSubject = $event"
            @add-question="addSubjectQuestion"
            @update-answer="updateAnswer"
            @add-floating-note="addFloatingNote"
          />

          <!-- Student Questions -->
          <QuestionSection
            :title="`${selectedStudent} Studentname`"
            :questions="filteredStudentQuestions"
            :can-create="canCreateQuestions"
            :can-answer="canAnswerQuestions"
            question-type="student"
            color="pink"
            :show-dropdown="true"
            :dropdown-options="students"
            :dropdown-value="selectedStudent"
            dropdown-label="Student Selection"
            @dropdown-change="selectedStudent = $event"
            @add-question="addStudentQuestion"
            @update-answer="updateAnswer"
            @add-floating-note="addFloatingNote"
          />
        </div>

        <!-- Right Column - Notes and Actions -->
        <div class="col-lg-4">
          <!-- General Notes -->
          <GeneralNotes
            :notes="generalNotes"
            :can-write="canWriteNotes"
            @add-note="addGeneralNote"
          />

          <!-- Actions -->
          <ActionChecklist
            :actions="actions"
            :can-create="canCreateActions"
            :can-complete="canCompleteActions"
            @add-action="addAction"
            @toggle-action="toggleAction"
          />
        </div>
      </div>
    </main>

    <!-- Floating Notes -->
    <FloatingNote
      v-for="note in floatingNotes"
      :key="note.id"
      :note="note"
      :style="{ top: note.position.y + 'px', left: note.position.x + 'px' }"
    />

    <!-- Save Success/Error Messages -->
    <div v-if="saveMessage" :class="['alert', saveMessage.type === 'success' ? 'alert-success' : 'alert-danger', 'save-message']">
      {{ saveMessage.text }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useDocumentData } from '../composables/useDocumentData.js'
import { useUserRole } from '../composables/useUserRole.js'
import BaseButton from './ui/BaseButton.vue'
import QuestionSection from './QuestionSection.vue'
import GeneralNotes from './GeneralNotes.vue'
import ActionChecklist from './ActionChecklist.vue'
import FloatingNote from './FloatingNote.vue'

const props = defineProps({
  classId: {
    type: [String, Number],
    required: true
  },
  documentId: {
    type: [String, Number],
    required: true
  }
})

const router = useRouter()

// Composables
const {
  document,
  overallQuestions,
  filteredSubjectQuestions,
  filteredStudentQuestions,
  subjects,
  students,
  selectedSubject,
  selectedStudent,
  generalNotes,
  actions,
  floatingNotes,
  addOverallQuestion,
  addSubjectQuestion,
  addStudentQuestion,
  updateAnswer,
  addGeneralNote,
  addAction,
  toggleAction,
  addFloatingNote,
  saveDocument
} = useDocumentData(props.classId, props.documentId)

const {
  canCreateQuestions,
  canAnswerQuestions,
  canCreateActions,
  canCompleteActions,
  canWriteNotes
} = useUserRole()

// Local state
const isSaving = ref(false)
const saveMessage = ref(null)

// Methods
const goBack = () => {
  router.push('/')
}

const handleSave = async () => {
  isSaving.value = true
  saveMessage.value = null
  
  try {
    const result = await saveDocument()
    saveMessage.value = {
      type: result.success ? 'success' : 'error',
      text: result.message
    }
    
    // Clear message after 3 seconds
    setTimeout(() => {
      saveMessage.value = null
    }, 3000)
  } catch (error) {
    saveMessage.value = {
      type: 'error',
      text: 'An unexpected error occurred while saving.'
    }
  } finally {
    isSaving.value = false
  }
}

onMounted(() => {
  // Any initialization logic here
  console.log('Document view mounted for class:', props.classId, 'document:', props.documentId)
})
</script>

<style scoped>
.document-view {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.document-header {
  background: white;
  border-bottom: 1px solid #dee2e6;
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.save-message {
  position: fixed;
  top: 100px;
  right: 20px;
  z-index: 1000;
  min-width: 300px;
}
</style>
