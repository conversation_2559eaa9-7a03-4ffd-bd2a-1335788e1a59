<template>
  <BaseCard title="General Notes" class="mb-4">
    <!-- Notes List -->
    <div class="notes-list">
      <div v-if="notes.length === 0" class="text-center text-muted py-3">
        <i class="bi bi-sticky fs-1 mb-2"></i>
        <p class="mb-0">No notes yet</p>
      </div>
      
      <div v-else>
        <div
          v-for="note in notes"
          :key="note.id"
          class="note-item"
        >
          <div class="note-content">
            <p class="note-text mb-1">{{ note.text }}</p>
            <small class="text-muted">
              by {{ note.author }} • {{ formatTimestamp(note.timestamp) }}
            </small>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Note Input -->
    <div v-if="canWrite" class="mt-3">
      <div class="mb-2">
        <textarea
          v-model="newNoteText"
          @keyup.ctrl.enter="handleAddNote"
          class="form-control"
          rows="3"
          placeholder="Add a general note... (Ctrl+Enter to save)"
        ></textarea>
      </div>
      <div class="d-flex justify-content-end">
        <BaseButton
          @click="handleAddNote"
          variant="purple"
          size="sm"
          icon="bi-plus-lg"
          :disabled="!newNoteText.trim()"
        >
          Add Note
        </BaseButton>
      </div>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref } from 'vue'
import BaseCard from './ui/BaseCard.vue'
import BaseButton from './ui/BaseButton.vue'

const props = defineProps({
  notes: {
    type: Array,
    default: () => []
  },
  canWrite: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['add-note'])

const newNoteText = ref('')

const handleAddNote = () => {
  if (newNoteText.value.trim()) {
    emit('add-note', newNoteText.value.trim())
    newNoteText.value = ''
  }
}

const formatTimestamp = (timestamp) => {
  const now = new Date()
  const diff = now - timestamp
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return 'just now'
  if (minutes < 60) return `${minutes}m ago`
  if (hours < 24) return `${hours}h ago`
  if (days < 7) return `${days}d ago`
  return timestamp.toLocaleDateString()
}
</script>

<style scoped>
.notes-list {
  max-height: 300px;
  overflow-y: auto;
}

.note-item {
  padding: 0.75rem;
  border-bottom: 1px solid #f8f9fa;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  border-left: 3px solid #E8436E;
}

.note-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.note-text {
  color: #333;
  line-height: 1.4;
  white-space: pre-wrap;
}

.form-control:focus {
  border-color: #E8436E;
  box-shadow: 0 0 0 0.2rem rgba(232, 67, 110, 0.25);
}
</style>
